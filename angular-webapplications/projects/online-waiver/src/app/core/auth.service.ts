import { Injectable, inject } from '@angular/core';
import { Router } from '@angular/router';
import { CookieService } from 'lib-app-core';

@Injectable({
    providedIn: 'root',
})
export class AuthService {
    private router = inject(Router);
    private cookieService = inject(CookieService);

    logout(): void {
        // Clear the customerId cookie
        this.cookieService.deleteCookie('customerId');

        // Navigate to login page
        this.router.navigate(['/auth/login']);
    }
}

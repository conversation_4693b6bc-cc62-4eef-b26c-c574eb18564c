/**
 * @fileoverview Concrete implementation of RefreshWebApiTokenInterceptor to handle 401 errors by logging out the user and redirecting to site selection
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-09-03
 */

import { Injectable, inject } from '@angular/core';
import { CookieService, RefreshWebApiTokenBaseInterceptor } from 'lib-app-core';
import { Router } from '@angular/router';
import { ToastService } from 'lib-ui-kit';

@Injectable()
export class RefreshWebApiTokenInterceptor extends RefreshWebApiTokenBaseInterceptor {
    private readonly _cookieService = inject(CookieService);
    private readonly _toastService = inject(ToastService);
    private readonly _router = inject(Router);

    protected handleLogout(): void {
        // Clear all execution context related cookies and navigate to the landing page
        const toastHeader = $localize`:refreshWebApiTokenInterceptor.toastHeaderText@@refreshWebApiTokenInterceptor.toastHeaderText:Logged out successfully`;
        const toastMessage = $localize`:refreshWebApiTokenInterceptor.toastMessageText@@refreshWebApiTokenInterceptor.toastMessageText:You have been logged out due to inactivity.`;
        this._cookieService.deleteCookie('webApiToken');
        this._cookieService.deleteCookie('customerId');
        this._cookieService.deleteCookie('siteId');
        this._toastService.warning(toastHeader, toastMessage);
        this._router.navigate(['/']); // Navigate to the site selection page
    }
}

/**
 * @fileoverview Waiver-related interfaces for the online waiver system
 * @description This file contains TypeScript interfaces that define the data structures
 *              for waiver containers, signing options, and waiver processing operations.
 *              These interfaces are used throughout the application for managing waiver
 *              documents, signing processes, and customer interactions.
 * <AUTHOR> G
 * @version 1.0.0
 * @created 2025-08-15
 */

/**
 * WaiverContainerDTO - Represents a single waiver document container
 *
 * This interface defines the structure for individual waiver documents,
 * including their content, validity periods, and file formats. Each waiver
 * container represents a specific waiver document that customers can sign.
 */
import { UserAddressDTOModel } from 'lib/lib-auth/src/lib/models/user-address-dto.model';
import { UserContactDTOModel } from 'lib/lib-auth/src/lib/models/user-contact-dto.model';
import { UserProfileDTOModel } from 'lib/lib-auth/src/lib/models/user-profile-dto.model';
import { CustomDataSetDTOModel } from 'lib/lib-auth/src/lib/models/custom-data-set-dto.model';
import { CustomerDTO } from '../models/customer-data-dto.model';
import { CustomerSignedWaiverDTO } from '../models/customer-signed-dto.model';
export interface WaiverContainerDTO {
    Name: string;
    WaiverFileName: string;
    ValidForDays: number | null;
    EffectiveDate: string;
    EffectiveTillDate: string | null;
    WaiverSetDetailId: number;
    WaiverHTML: string | null;
    WaiverPdfBase64: string | null;
}

/**
 * WaiverSetSigningOptionsContainerDTO - Represents signing options for waiver sets
 *
 * This interface defines the available signing options and methods for a waiver set.
 * It includes lookup values and descriptions for different signing approaches
 * (e.g., digital signature, parent signature, guardian signature).
 */
export interface WaiverSetSigningOptionsContainerDTO {
    LookupValueId: number;
    OptionName: string;
    OptionDescription: string;
}

/**
 * WaiverSetContainerDTO - Represents a collection of related waivers
 *
 * This interface defines a waiver set that contains multiple related waiver documents
 * and their associated signing options. A waiver set typically represents all waivers
 * required for a specific activity, product, or service.
 */
export interface WaiverSetContainerDTO {
    WaiverSetId: number;
    Name: string;
    WaiversContainerDTO: WaiverContainerDTO[];
    WaiverSetSigningOptionsContainerDTO: WaiverSetSigningOptionsContainerDTO[];
}

/**
 * WaiverSetResponse - API response wrapper for waiver set data
 *
 * This interface defines the structure of API responses that return waiver set
 * information, including a hash for data integrity verification.
 */
export interface WaiverSetResponse {
    data: {
        WaiverSetContainerDTOList: WaiverSetContainerDTO[];
        Hash: string;
    };
}

/**
 * WaiverInfo - Core waiver information for processing
 *
 * This interface contains essential information needed to process waivers,
 * including site identification, product details, and transaction references.
 * Used as a simplified data structure for waiver processing operations.
 */
export interface WaiverInfo {
    waiverSetId: number;
    siteId: number;
    productName: string;
    transactionId: number;
}

/**
 * GetHtmlWaiverQueryParams - Query parameters for retrieving HTML waiver content
 *
 * This interface defines the parameters required when requesting HTML waiver
 * content, including customer information and waiver set identification.
 */
export interface GetHtmlWaiverQueryParams {
    defaultCustomer: string;
    waiverSetId: string;
}

/**
 * GetPreviewHtmlWaiverQueryParams - Query parameters for preview HTML waiver content
 *
 * Includes site and language identifiers along with the waiver set id.
 */
export interface GetPreviewHtmlWaiverQueryParams {
    siteId: string;
    languageId: string;
    waiverSetId: string;
}

/**
 * SignForGuestCustomersDTO - Represents guest customer signing information
 *
 * This interface defines the structure for guest customers who need to sign
 * waivers, typically used when customers sign on behalf of others (e.g., parents
 * signing for children).
 */
export interface SignForGuestCustomersDTO {
    Id: number;
}

/**
 * GetHtmlWaiverPayload - Request payload for HTML waiver generation
 *
 * This interface defines the complete payload structure for requesting HTML
 * waiver content, including channel information, customer lists, and signing
 * configurations for both regular and guest customers.
 */
export interface GetHtmlWaiverPayload {
    Channel: string;
    SignForCustomersIdList: number[];
    SignatoryGuestCustomerDTO: {};
    SignForGuestCustomersDTOList: SignForGuestCustomersDTO[];
}

/**
 * WaiverPreviewData - Represents waiver content for preview purposes
 *
 * This interface defines the structure for waiver preview data, including
 * the content format (HTML or PDF) and the actual content data.
 * Used for displaying waiver content to users before signing.
 */
export interface WaiverPreviewData {
    base64Data: string;
    type: 'html' | 'pdf';
}

/**
 * WaiverDataWithInfo - Combined waiver data and preview information
 *
 * This interface combines waiver container information with preview data,
 * providing a complete structure for displaying waiver content along with
 * its metadata and configuration information.
 */
export interface WaiverDataWithInfo {
    waiver: WaiverContainerDTO;
    data: WaiverPreviewData;
}

/**
 * SignHTMLWaiverParams - Parameters for signing HTML waivers
 *
 * This interface defines the parameters required when signing HTML waivers,
 * including waiver set identification, channel information, and customer
 * signing configurations.
 */
export interface SignHTMLWaiverPayload {
    WaiverSetId: number;
    Channel: string;
    ApprovingManagerId: number;
    SignatoryGuestCustomerDTO: any;
    CreateCustomerSignedWaiverDTOList: Array<{
        WaiverSetDetailId: number;
        SignForCustomersIdList: number[];
        DocumentIdentifier: string;
        SignaturesAndAcceptanceEncoded: string;
        SignForGuestCustomersDTOList: any[];
    }>;
}

/**
 * SignHTMLWaiverQueryParams - Query parameters for signing HTML waivers
 *
 * This interface defines the parameters required when signing HTML waivers,
 * including customer identification and waiver set identification.
 */
export interface SignHTMLWaiverQueryParams {
    CustomerId: string;
    WaiverSetId: string;
}

/**
 * SignHTMLWaiverResponse - Response structure for HTML waiver signing
 *
 * This interface defines the structure of the response from the HTML waiver
 * signing API, including the signed waiver data and any associated metadata.
 */
export interface SignHTMLWaiverResponse {
    data?: string;
}

/**
 * SignForCustomerDTO - Represents a customer who needs to sign a waiver
 *
 * This interface defines the structure for customers who need to sign waivers,
 * including their contact information, profile details, and address information.
 */
export type SignForCustomerDTO = CustomerDTO;
export type ContactDTO = UserContactDTOModel;
export type ProfileDTO = UserProfileDTOModel;
export type AddressDTO = UserAddressDTOModel;
export type CustomDataSetDTO = CustomDataSetDTOModel;

/**
 * CustomerSignedWaiverHeaderDTO - Represents a signed waiver header
 *
 * This interface defines the structure for a signed waiver header,
 * including metadata about the waiver, customer information, and signing details.
 */
export interface CustomerSignedWaiverHeaderDTO {
    CustomerSignedWaiverHeaderId: number;
    SignedBy: number;
    SignedDate: string;
    Channel: string;
    PosMachineId: number;
    IsActive: boolean;
    WaiverCode: string;
    Guid: string;
    SynchStatus: boolean;
    MasterEntityId: number;
    CreatedBy: string;
    CreationDate: string;
    LastUpdatedBy: string;
    LastUpdateDate: string;
    SiteId: number;
    CustomerSignedWaiverDTOList: CustomerSignedWaiverDTO[];
    IsChangedRecursive: boolean;
    IsChanged: boolean;
}

/**
 * SignPDFWaiverResponse - Response structure for PDF waiver signing
 *
 * This interface defines the structure of the response from the PDF waiver
 * signing API, including the signed waiver data and any associated metadata.
 */
export interface SignPDFWaiverResponse {
    data: CustomerSignedWaiverHeaderDTO[];
}

/**
 * CustomerContentDTO - Represents customer content for waiver signing
 *
 * This interface defines the structure for customer content, including
 * their contact information, profile details, and address information.
 */
export interface CustomerContentDTO {
    CustomerId: number;
    CustomerName: string;
    PhoneNumber: string;
    EmailId: string;
    CustomerDOB: string;
    Attribute1Name: string;
    Attribute2Name: string;
    WaiverCustomAttributeList: any[];
}

/**
 * WaiversDTO - Represents a waiver
 *
 * This interface defines the structure for a waiver, including its
 * identification, name, and other relevant information.
 */
export interface WaiversDTO {
    waiverSetId: number;
    waiverSetDetailId: number;
    name: string;
    waiverFileName: string;
    isActive: boolean;
    validForDays: number | null;
}

/**
 * SignatoryCustomerDTO - Represents a signatory customer
 *
 * This interface defines the structure for a signatory customer,
 * including their contact information, profile details, and address information.
 */
export interface SignatoryCustomerDTO extends SignForCustomerDTO {}

/**
 * CustIdSignatureImageBase64DTO - Represents a customer ID and signature image base64
 *
 * This interface defines the structure for a customer ID and signature image base64,
 * including their contact information, profile details, and address information.
 */
export interface CustIdSignatureImageBase64DTO {
    Key: number;
    Value: string;
}

/**
 * SignPDFWaiverPayload - Request payload for PDF waiver signing
 *
 * This interface defines the complete payload structure for requesting PDF
 * waiver content, including channel information, customer lists, and signing
 * configurations for both regular and guest customers.
 */
export interface SignPDFWaiverPayload {
    SignForCustomerDTOList: SignForCustomerDTO[];
    CustomerContentDTOList: CustomerContentDTO[];
    WaiversDTO: WaiversDTO;
    SignatoryCustomerDTO: SignatoryCustomerDTO;
    CustIdSignatureImageBase64List: CustIdSignatureImageBase64DTO[];
    Channel: string;
}

/**
 * ISignWaiverQueryParams - Query parameters for signing waivers
 *
 * This interface defines the parameters required when signing waivers,
 * including waiver set identification.
 */
export interface ISignWaiverQueryParams {
    waiverSetId?: string;
}

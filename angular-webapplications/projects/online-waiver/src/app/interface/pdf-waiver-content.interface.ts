/**
 * @fileoverview PDF and Waiver Content interfaces for the online waiver system
 * @description This file contains TypeScript interfaces that define the data structures
 *              for PDF rendering, waiver content management, and HTML waiver signing.
 *              These interfaces are used throughout the application for managing PDF
 *              documents, waiver content processing, and signing workflows.
 * <AUTHOR> G
 * @version 1.0.0
 * @created 2025-09-02
 */

import { ElementRef, ChangeDetectorRef } from '@angular/core';
import { Params } from '@angular/router';
import { CustomerSignedWaiverDTO } from '../models/customer-signed-waiver-dto.model';

/**
 * Result of PDF conversion operations
 */
export interface PdfConversionResult {
    blobUrl: string;
    blob: Blob;
}

/**
 * Options for PDF rendering operations
 */
export interface PdfRenderingOptions {
    container: ElementRef<HTMLDivElement>;
    pdfData: string;
    changeDetectorRef: ChangeDetectorRef;
    onPageNumbersUpdate?: (pageNumbers: number[]) => void;
    onRenderingStateChange?: (isRendering: boolean) => void;
}

/**
 * Data structure for signed waiver preview operations
 */
export interface SignedWaiverPreviewData {
    content: CustomerSignedWaiverDTO | null;
    title: string;
    waiverSetId: number;
}

/**
 * HTML waiver data structure
 */
export interface HtmlWaiverData {
    CustomerWaiverHTMLForm: string;
    SuccessButtonURL: string;
    CancelButtonURL: string;
    SignMessage: string;
    WaiverType: string;
}

/**
 * Callback functions for HTML waiver operations
 */
export interface HtmlWaiverCallbacks {
    onSuccess: (waiverCode?: string) => void;
    onCancel: () => void;
    onError: (error: string) => void;
}

/**
 * Data required for HTML waiver signing operations
 */
export interface HtmlWaiverSigningData {
    waiverSetId: number;
    waiverSetDetailId: number;
    signatureData: string;
    customerId: number;
    relatedCustomerIds: number[];
}

/**
 * Route parameters interface for type safety
 */
export interface SignWaiverRouteParams extends Params {
    waiverSetId: string;
}

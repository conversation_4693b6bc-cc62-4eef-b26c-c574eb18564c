/**
 * @fileoverview Base64 PDF business layer service with modal functionality
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-05
 */
import {
    Injectable,
    inject,
    PLATFORM_ID,
    signal,
    ElementRef,
    ChangeDetectorRef,
} from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { RequestState } from 'lib-app-core';
import { PdfRenderingOptions } from '../../interface/pdf-waiver-content.interface';

@Injectable()
export class Base64PdfBLService extends RequestState {
    private readonly platformId = inject(PLATFORM_ID);
    readonly PDF_CONTENT_TYPE = 'pdf';
    readonly PDF_DATA_URL_PREFIX = /^data:application\/pdf;base64,/;
    readonly BASE64_CHUNK_SIZE = 512;

    // Modal state management
    readonly showModal = signal(false);
    readonly modalContent = signal('');
    readonly modalTitle = signal('');
    readonly pdfBlobUrl = signal<string | null>(null);

    /**
     * Renders PDF using PDF.js canvas rendering - single source of truth for all components
     * Handles both simple rendering and complex callback scenarios
     */
    async renderPdfWithPdfJs(
        options: PdfRenderingOptions | string,
        container?: ElementRef<HTMLDivElement> | HTMLDivElement,
        onRenderingStart?: () => void,
        onRenderingComplete?: () => void,
        onError?: (error: string) => void,
        onPageNumbersUpdate?: (pageNumbers: number[]) => void
    ): Promise<void> {
        // Handle both new and legacy call patterns
        let pdfData: string;
        let containerElement: HTMLDivElement | null = null;
        let changeDetectorRef: ChangeDetectorRef | null = null;
        let onRenderingStateChange:
            | ((isRendering: boolean) => void)
            | undefined;

        if (typeof options === 'string') {
            // Legacy pattern: renderPdfWithPdfJs(pdfData, container, callbacks...)
            pdfData = options;
            if (container instanceof ElementRef) {
                containerElement = container.nativeElement;
            } else if (container instanceof HTMLDivElement) {
                containerElement = container;
            }
        } else {
            // New pattern: renderPdfWithPdfJs({ container, pdfData, changeDetectorRef, callbacks... })
            pdfData = options.pdfData;
            containerElement = options.container?.nativeElement || null;
            changeDetectorRef = options.changeDetectorRef;
            onPageNumbersUpdate = options.onPageNumbersUpdate;
            onRenderingStateChange = options.onRenderingStateChange;
        }

        if (!pdfData || !containerElement) {
            if (onError) onError('PDF data or container not available');
            return;
        }

        // Set loading state
        if (onRenderingStart) onRenderingStart();
        if (onRenderingStateChange) onRenderingStateChange(true);

        // Wait for container to be available if using change detector
        if (changeDetectorRef) {
            let retryCount = 0;
            const maxRetries = 10;
            const retryDelay = 100;

            while (!containerElement && retryCount < maxRetries) {
                changeDetectorRef.detectChanges();
                await new Promise((resolve) => setTimeout(resolve, retryDelay));
                retryCount++;
            }

            if (!containerElement) {
                const errorMsg =
                    'PDF container not available after multiple retries';
                console.error(errorMsg);
                if (onError) onError(errorMsg);
                if (onRenderingStateChange) onRenderingStateChange(false);
                return;
            }
        }

        try {
            // Check if we're in browser environment
            if (!isPlatformBrowser(this.platformId)) {
                const errorMsg = 'PDF rendering not available on server';
                if (onError) onError(errorMsg);
                if (onRenderingStateChange) onRenderingStateChange(false);
                return;
            }

            // PDF.js should be available globally from ngx-extended-pdf-viewer
            const pdfjsLib = (window as any)['pdfjsLib'];
            if (!pdfjsLib) {
                const errorMsg = 'PDF.js not loaded';
                if (onError) onError(errorMsg);
                if (onRenderingStateChange) onRenderingStateChange(false);
                return;
            }

            // Convert base64 to Uint8Array
            const cleanBase64 = pdfData.replace(
                /^data:application\/pdf;base64,/,
                ''
            );
            const binaryString = atob(cleanBase64);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }

            // Load PDF document
            const loadingTask = pdfjsLib.getDocument({ data: bytes });
            const pdf = await loadingTask.promise;

            // Set page numbers for rendering
            const numPages = pdf.numPages;
            const pageNumbers = Array.from(
                { length: numPages },
                (_, i) => i + 1
            );
            if (onPageNumbersUpdate) onPageNumbersUpdate(pageNumbers);

            // Clear container and create canvas elements
            containerElement.innerHTML = '';

            // Get container dimensions for scaling
            const containerRect = containerElement.getBoundingClientRect();
            const containerWidth = containerRect.width - 32; // Account for padding

            // Render each page individually
            for (let pageNum = 1; pageNum <= numPages; pageNum++) {
                // Create canvas for this page
                const canvas = document.createElement('canvas');
                canvas.id = `pdfPage${pageNum}`;
                canvas.className = 'w-full max-w-full h-auto';

                // Create container for this page
                const pageContainer = document.createElement('div');
                pageContainer.className = 'flex justify-center mb-4';
                pageContainer.appendChild(canvas);
                containerElement.appendChild(pageContainer);

                // Get the page
                const page = await pdf.getPage(pageNum);
                const viewport = page.getViewport({ scale: 1.0 });

                // Calculate scale
                const scale = containerWidth / viewport.width;
                const minScale = 0.8;
                const maxScale = 2.0;
                let finalScale = Math.max(minScale, Math.min(maxScale, scale));

                // For mobile, try to use more available space
                if (window.innerWidth <= 768) {
                    finalScale = Math.max(finalScale, 1.0);
                }

                const scaledViewport = page.getViewport({ scale: finalScale });

                // Set canvas dimensions
                canvas.width = scaledViewport.width;
                canvas.height = scaledViewport.height;

                const context = canvas.getContext('2d');
                const renderContext = {
                    canvasContext: context!,
                    viewport: scaledViewport,
                };

                await page.render(renderContext).promise;
            }

            // Success callbacks
            if (onRenderingComplete) onRenderingComplete();
            if (onRenderingStateChange) onRenderingStateChange(false);
        } catch (error) {
            const errorMsg =
                error instanceof Error ? error.message : 'Failed to render PDF';
            console.error('Error rendering PDF:', error);
            if (onError) onError(errorMsg);
            if (onRenderingStateChange) onRenderingStateChange(false);
        }
    }

    /**
     * Validates the PDF content before processing
     * @param base64 - The base64 string to validate
     * @returns boolean
     */
    validatePdfContent(base64: string): boolean {
        if (!base64) return false;

        // Remove the data URL prefix and any whitespace
        const cleanBase64 = base64
            .replace(this.PDF_DATA_URL_PREFIX, '')
            .replace(/\s/g, '');

        try {
            if (isPlatformBrowser(this.platformId)) {
                atob(cleanBase64);
            } else {
                Buffer.from(cleanBase64, 'base64');
            }
            return true;
        } catch {
            return false;
        }
    }

    /**
     * Converts base64 string to Blob (safe for browser)
     * @param base64 - The base64 string to convert to Blob
     * @returns Blob
     */
    convertBase64ToBlob(base64: string): Blob {
        const cleanBase64 = base64.replace(this.PDF_DATA_URL_PREFIX, '');

        let binaryString: string;

        if (isPlatformBrowser(this.platformId)) {
            binaryString = atob(cleanBase64);
        } else {
            const buffer = Buffer.from(cleanBase64, 'base64');
            binaryString = String.fromCharCode(...buffer);
        }

        const byteArrays = [];
        for (
            let offset = 0;
            offset < binaryString.length;
            offset += this.BASE64_CHUNK_SIZE
        ) {
            const slice = binaryString.slice(
                offset,
                offset + this.BASE64_CHUNK_SIZE
            );
            const byteNumbers = Array.from(slice, (c) => c.charCodeAt(0));
            byteArrays.push(new Uint8Array(byteNumbers));
        }

        return new Blob(byteArrays, { type: 'application/pdf' });
    }

    /**
     * Converts base64 PDF to a secure blob URL
     * @param base64 - The base64 string to convert to Blob
     * @returns Blob
     */
    convertBase64ToPdfUrl(base64: string): string | null {
        if (!this.validatePdfContent(base64)) {
            return null;
        }

        const blob = this.convertBase64ToBlob(base64);
        return `${URL.createObjectURL(blob)}#toolbar=0&navpanes=0&zoom=90`;
    }

    /**
     * Clean up blob URL
     * @param blobUrl - The blob URL to clean up
     */
    cleanupBlobUrl(blobUrl: string | null): void {
        if (blobUrl) {
            URL.revokeObjectURL(blobUrl);
        }
    }

    /**
     * Closes the PDF modal and cleans up resources.
     *
     * This method:
     * 1. Sets the modal visibility to false
     * 2. Cleans up the PDF blob URL to prevent memory leaks
     *
     * Should be called when the user closes the modal or navigates away.
     *
     * @returns {void} Nothing is returned
     */
    closeModal(): void {
        this.showModal.set(false);
        this.cleanupPdfBlobUrl();
    }

    /**
     * Shows a fallback message in the modal when PDF content cannot be displayed.
     *
     * This method is used as a fallback when:
     * - PDF conversion fails
     * - Content is not available
     * - Other display errors occur
     *
     * @param {string} message - The error message to display to the user
     * @param {string} title - The title to show in the modal header
     * @returns {void} Nothing is returned
     * @protected
     */
    protected showFallback(message: string, title: string): void {
        this.modalContent.set(message);
        this.modalTitle.set(title);
        this.showModal.set(true);
    }

    /**
     * Cleans up the current PDF blob URL to prevent memory leaks.
     *
     * This method:
     * 1. Retrieves the current blob URL from the signal
     * 2. Calls the cleanup service to revoke the blob URL
     * 3. Resets the blob URL signal to null
     *
     * Should be called before creating a new blob URL or when closing the modal.
     *
     * @returns {void} Nothing is returned
     * @protected
     */
    protected cleanupPdfBlobUrl(): void {
        const currentBlobUrl = this.pdfBlobUrl();
        if (currentBlobUrl) {
            this.cleanupBlobUrl(currentBlobUrl);
            this.pdfBlobUrl.set(null);
        }
    }

    /**
     * Downloads a blob as a file
     *
     * @param {Blob} blob - The blob to download
     * @param {string} fileName - The name of the file to download
     * @returns {void} Nothing is returned
     * @protected
     */
    protected downloadBlob(blob: Blob, fileName: string): void {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }
}

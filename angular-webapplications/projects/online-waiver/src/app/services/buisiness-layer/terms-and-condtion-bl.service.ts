/**
 * @fileoverview Terms and conditions business layer service
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-05
 */
import { computed, inject, Injectable, signal } from '@angular/core';
import {
    RichContentContainerDTOModel,
    RichContentDTOModel,
    RichContentsServiceDL,
} from 'lib-app-core';
import { Base64PdfBLService } from './base64-pdf-bl.service';

export interface TermsAndConditionsData {
    content: RichContentDTOModel | null;
    title: string;
}

@Injectable()
export class TermsAndConditionsBL extends Base64PdfBLService {
    private readonly _richContentsServiceDL = inject(RichContentsServiceDL);

    // Additional reactive state specific to terms and conditions
    readonly richContents = signal<RichContentDTOModel[]>([]);

    // Override modal title for terms and conditions
    override readonly modalTitle = signal('Terms and Conditions');

    readonly termsAndConditions = computed(() => {
        const richContents = this.richContents();
        return richContents.find(this.isPdfTermsAndConditions) || null;
    });

    readonly termsAndConditionsData = computed((): TermsAndConditionsData => {
        const content = this.termsAndConditions();
        return {
            content,
            title: content?.ContentName || 'Terms and Conditions',
        };
    });

    /**
     * Opens the terms and conditions modal with PDF content.
     *
     * This method performs the following operations:
     * 1. Retrieves the current terms and conditions data
     * 2. Cleans up any existing PDF blob URL to prevent memory leaks
     * 3. Converts the base64 PDF data to a blob URL for display
     * 4. Opens the modal with the PDF content or shows a fallback message
     *
     * If no content is available or PDF conversion fails, a fallback message is displayed.
     *
     * @returns {void} Nothing is returned
     */
    openTermsAndConditions(): void {
        const { content, title } = this.termsAndConditionsData();
        if (!content) return;

        this.cleanupPdfBlobUrl();

        const blobUrl = this.convertBase64ToPdfUrl(content.Data);
        if (!blobUrl) {
            this.showFallback('Failed to convert PDF content.', title);
            return;
        }

        this.pdfBlobUrl.set(blobUrl);
        this.modalTitle.set(title);
        this.showModal.set(true);
    }

    /**
     * Determines if a rich content item represents PDF terms and conditions.
     *
     * This method checks multiple criteria to identify terms and conditions PDFs:
     * - Content type includes 'application/pdf'
     * - File name ends with '.pdf'
     * - Content name contains both 'pdf' and ('terms' or 'conditions')
     *
     * The search is case-insensitive for better matching.
     *
     * @param {RichContentDTOModel} content - The rich content item to evaluate
     * @returns {boolean} True if the content is identified as PDF terms and conditions, false otherwise
     * @private
     */
    private isPdfTermsAndConditions(content: RichContentDTOModel): boolean {
        const name = content.ContentName?.toLowerCase() || '';
        const type = content.ContentType?.toLowerCase() || '';
        const file = content.FileName?.toLowerCase() || '';

        return (
            type.includes('application/pdf') ||
            file.endsWith('.pdf') ||
            (name.includes('pdf') &&
                (name.includes('terms') || name.includes('conditions')))
        );
    }

    loadRichContents(): void {
        this._handleRequest(
            this._richContentsServiceDL.load(),
            (response: RichContentContainerDTOModel) => {
                this.richContents.set(response.data);
            }
        );
    }
}

/**
 * @fileoverview Business layer service for getting signed waiver content
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-15
 */

import { Injectable, inject, signal, computed } from '@angular/core';
import { Observable, catchError, of, map, finalize } from 'rxjs';
import { GetSignedWaiverContentDL } from '../data-layer/get-signed-waiver-content-dl.service';
import { CookieService } from 'lib-app-core';
import { CustomerSignedWaiverContentResponseDTO } from '../../models/customer-signed-waiver-content-response-dto.model';
import { CustomerSignedWaiverDTO } from '../../models/customer-signed-waiver-dto.model';
import { Base64PdfBLService } from '../buisiness-layer/base64-pdf-bl.service';
import { SignedWaiverPreviewData } from '../../interface/pdf-waiver-content.interface';

@Injectable({
    providedIn: 'root',
})
export class GetSignedWaiverContentBL extends Base64PdfBLService {
    private _getSignedWaiverContentDL = inject(GetSignedWaiverContentDL);
    private _cookieService = inject(CookieService);
    private customerId = this._cookieService.getCookie('userId') ?? '';

    // Reactive state
    readonly signedWaivers = signal<CustomerSignedWaiverDTO[]>([]);

    // Separate signal for current preview waiver (don't overwrite main array)
    readonly currentPreviewWaiver = signal<CustomerSignedWaiverDTO | null>(
        null
    );

    // Loading states for UI skeleton loaders
    readonly isLoading = signal(false);

    // Override modal title for signed waivers
    override readonly modalTitle = signal('Signed Waiver Preview');

    readonly signedWaiverPreview = computed(() => {
        // Use currentPreviewWaiver if available, otherwise fall back to first signedWaiver
        const previewWaiver = this.currentPreviewWaiver();
        if (previewWaiver) {
            return previewWaiver;
        }
        const signedWaivers = this.signedWaivers();
        return signedWaivers.length > 0 ? signedWaivers[0] : null;
    });

    readonly signedWaiverPreviewData = computed((): SignedWaiverPreviewData => {
        const content = this.signedWaiverPreview();
        return {
            content,
            title: content?.WaiverName || 'Signed Waiver Preview',
            waiverSetId: content?.WaiverSetId || 0,
        };
    });

    /**
     * Determines if a waiver is HTML or PDF based on content
     * @param waiver - The signed waiver to check
     * @returns 'html' | 'pdf' based on waiver content
     */
    private determineWaiverType(
        waiver: CustomerSignedWaiverDTO
    ): 'html' | 'pdf' {
        if (waiver.SignedWaiverFileContentInBase64Format) {
            try {
                const decodedContent = atob(
                    waiver.SignedWaiverFileContentInBase64Format
                );

                // Check if decoded content looks like HTML
                if (
                    decodedContent.includes('<html') ||
                    decodedContent.includes('<!DOCTYPE') ||
                    decodedContent.includes('<body') ||
                    decodedContent.includes('<div')
                ) {
                    return 'html';
                }
            } catch (error) {
                // If base64 decode fails, assume it's PDF
                return 'pdf';
            }
        }
        return 'pdf'; // Default to PDF if no content
    }

    /**
     * Gets waiver type for the current signed waivers
     * @returns 'html' | 'pdf' based on waiver content
     */
    getWaiverType(): 'html' | 'pdf' {
        const signedWaivers = this.signedWaivers();

        if (signedWaivers.length > 0) {
            const waiverType = this.determineWaiverType(signedWaivers[0]);
            return waiverType;
        }
        return 'pdf'; // Default to PDF
    }

    /**
     * Gets data structure for waiver success popup
     * @returns WaiverSuccessPopupData with appropriate structure
     */
    getWaiverSuccessPopupData(): {
        waiverType: 'html' | 'pdf';
        signedWaivers: CustomerSignedWaiverDTO[];
        waiverSetId?: number;
        waiverCode?: string;
    } {
        const signedWaivers = this.signedWaivers();
        const waiverType = this.getWaiverType();

        return {
            waiverType,
            signedWaivers,
            waiverSetId: signedWaivers[0]?.WaiverSetId || undefined,
            waiverCode: signedWaivers[0]?.WaiverCode || undefined,
        };
    }

    /**
     * Gets all signed waiver content for a customer
     * @returns Observable of signed waiver content
     */
    getSignedWaiverContent(): Observable<CustomerSignedWaiverContentResponseDTO> {
        this._getSignedWaiverContentDL.buildApiParams({
            customerId: this.customerId,
        });
        return this._getSignedWaiverContentDL.load();
    }

    /**
     * Gets signed waiver content for a specific waiver set ID
     * @param waiverSetId - The waiver set ID to filter by
     * @returns Observable of signed waiver content
     */
    getSignedSingleWaiverContent(
        waiverSetId: number
    ): Observable<CustomerSignedWaiverContentResponseDTO> {
        return this.getSignedWaiverContent().pipe(
            map((response) => {
                if (response.data) {
                    // Extract all signed waivers for the specific waiverSetId in one pass
                    const signedWaivers: CustomerSignedWaiverDTO[] = [];

                    response.data.forEach((customer) => {
                        if (customer.CustomerSignedWaiverDTOList) {
                            const matchingWaivers =
                                customer.CustomerSignedWaiverDTOList.filter(
                                    (waiver) =>
                                        waiver.WaiverSetId === waiverSetId
                                );
                            signedWaivers.push(...matchingWaivers);
                        }
                    });

                    this.signedWaivers.set(signedWaivers);
                }
                return response;
            })
        );
    }

    /**
     * Gets signed waiver content for a specific waiver code
     * @param waiverCode - The waiver code to filter by
     * @returns Observable of signed waiver content
     */
    getSignedWaiverContentByCode(
        waiverCode: string
    ): Observable<CustomerSignedWaiverContentResponseDTO> {
        this.isLoading.set(true);

        return this.getSignedWaiverContent().pipe(
            map((response) => {
                if (response.data) {
                    // Extract all signed waivers for the specific waiverCode in one pass
                    const signedWaivers: CustomerSignedWaiverDTO[] = [];

                    response.data.forEach((customer) => {
                        if (customer.CustomerSignedWaiverDTOList) {
                            const matchingWaivers =
                                customer.CustomerSignedWaiverDTOList.filter(
                                    (waiver) => waiver.WaiverCode === waiverCode
                                );
                            signedWaivers.push(...matchingWaivers);
                        }
                    });

                    this.signedWaivers.set(signedWaivers);
                }
                return response;
            }),
            catchError((error) => {
                console.error('Error fetching signed waiver content:', error);
                return of({
                    data: [],
                    customersImage: '',
                    customersIdImage: '',
                    totalPages: 0,
                });
            }),
            finalize(() => {
                this.isLoading.set(false);
            })
        );
    }

    /**
     * Opens the signed waiver preview modal with PDF content.
     *
     * This method performs the following operations:
     * 1. Retrieves the current signed waiver data
     * 2. Cleans up any existing PDF blob URL to prevent memory leaks
     * 3. Converts the base64 PDF data to a blob URL for display
     * 4. Opens the modal with the PDF content or shows a fallback message
     *
     * If no content is available or PDF conversion fails, a fallback message is displayed.
     *
     * @param waiverSetId - The waiver set ID to preview
     * @returns {void} Nothing is returned
     */
    openSignedWaiverPreview(waiverSetId: number): void {
        // First get the signed waiver content for the specific waiverSetId
        this.getSignedSingleWaiverContent(waiverSetId).subscribe(() => {
            const { content, title } = this.signedWaiverPreviewData();

            if (!content?.SignedWaiverFileContentInBase64Format) {
                this.showFallback(
                    'No signed waiver content available for preview.',
                    title
                );
                return;
            }

            this.cleanupPdfBlobUrl();

            const blobUrl = this.convertBase64ToPdfUrl(
                content.SignedWaiverFileContentInBase64Format
            );

            if (!blobUrl) {
                this.showFallback('Failed to convert PDF content.', title);
                return;
            }

            this.pdfBlobUrl.set(blobUrl);
            this.modalTitle.set(title);
            this.showModal.set(true);
        });
    }

    /**
     * Opens the signed waiver preview modal with PDF content using WaiverCode.
     *
     * This method performs the following operations:
     * 1. Retrieves the current signed waiver data by WaiverCode
     * 2. Cleans up any existing PDF blob URL to prevent memory leaks
     * 3. Converts the base64 PDF data to a blob URL for display
     * 4. Opens the modal with the PDF content or shows a fallback message
     *
     * If no content is available or PDF conversion fails, a fallback message is displayed.
     *
     * @param waiverCode - The waiver code to preview
     * @returns {void} Nothing is returned
     */
    openSignedWaiverPreviewByCode(waiverCode: string): void {
        // Set loading state for skeleton loader
        this.isLoading.set(true);

        this.getSignedWaiverContentByCode(waiverCode).subscribe({
            next: () => {
                const { content, title } = this.signedWaiverPreviewData();

                if (!content?.SignedWaiverFileContentInBase64Format) {
                    this.isLoading.set(false);
                    this.showFallback(
                        'No signed waiver content available for preview.',
                        title
                    );
                    return;
                }

                this.cleanupPdfBlobUrl();
                const blobUrl = this.convertBase64ToPdfUrl(
                    content.SignedWaiverFileContentInBase64Format
                );

                if (!blobUrl) {
                    this.isLoading.set(false);
                    this.showFallback('Failed to convert PDF content.', title);
                    return;
                }

                this.pdfBlobUrl.set(blobUrl);
                this.modalTitle.set(title);
                this.showModal.set(true);

                // Clear loading state after preview is ready
                this.isLoading.set(false);
            },
            error: (error) => {
                console.error('Error opening preview:', error);
                this.isLoading.set(false);
                this.showFallback('Failed to load preview.', 'Error');
            },
        });
    }

    /**
     * Downloads the signed waiver PDF
     *
     * @param waiverSetId - The waiver set ID to download
     * @returns {void} Nothing is returned
     */
    downloadSignedWaiver(waiverSetId: number): void {
        this.getSignedSingleWaiverContent(waiverSetId).subscribe(() => {
            const { content } = this.signedWaiverPreviewData();

            if (!content?.SignedWaiverFileContentInBase64Format) {
                console.error(
                    'No signed waiver content available for download.'
                );
                return;
            }

            const blob = this.convertBase64ToBlob(
                content.SignedWaiverFileContentInBase64Format
            );
            const fileName =
                content.SignedWaiverFileName ||
                `signed-waiver-${waiverSetId}.pdf`;
            this.downloadBlob(blob, fileName);
        });
    }

    /**
     * Downloads the signed waiver PDF using WaiverCode
     *
     * @param waiverCode - The waiver code to download
     * @returns {void} Nothing is returned
     */
    downloadSignedWaiverByCode(waiverCode: string): void {
        // Set loading state for skeleton loader
        this.isLoading.set(true);

        this.getSignedWaiverContentByCode(waiverCode).subscribe({
            next: () => {
                const { content } = this.signedWaiverPreviewData();

                if (!content?.SignedWaiverFileContentInBase64Format) {
                    this.isLoading.set(false);
                    console.error(
                        'No signed waiver content available for download.'
                    );
                    return;
                }

                const blob = this.convertBase64ToBlob(
                    content.SignedWaiverFileContentInBase64Format
                );
                const fileName =
                    content.SignedWaiverFileName ||
                    `signed-waiver-${waiverCode}.pdf`;
                this.downloadBlob(blob, fileName);

                // Clear loading state after download is complete
                this.isLoading.set(false);
            },
            error: (error) => {
                console.error('Error downloading waiver:', error);
                this.isLoading.set(false);
            },
        });
    }

    /**
     * Downloads the signed waiver PDF for a specific user
     * @param customerSignedWaiverId - The specific customer signed waiver ID
     * @returns {void} Nothing is returned
     */
    downloadSignedWaiverForUser(customerSignedWaiverId: number): void {
        const signedWaivers = this.signedWaivers();
        const waiver = signedWaivers.find(
            (w) => w.CustomerSignedWaiverId === customerSignedWaiverId
        );

        if (!waiver?.SignedWaiverFileContentInBase64Format) {
            return;
        }

        const blob = this.convertBase64ToBlob(
            waiver.SignedWaiverFileContentInBase64Format
        );
        const fileName =
            waiver.SignedWaiverFileName ||
            `signed-waiver-${customerSignedWaiverId}.pdf`;
        this.downloadBlob(blob, fileName);
    }

    /**
     * Opens the signed waiver preview modal for a specific user
     * @param customerSignedWaiverId - The specific customer signed waiver ID
     * @returns {void} Nothing is returned
     */
    openSignedWaiverPreviewForUser(customerSignedWaiverId: number): void {
        const signedWaivers = this.signedWaivers();

        const waiver = signedWaivers.find(
            (w) => w.CustomerSignedWaiverId === customerSignedWaiverId
        );

        if (!waiver) {
            this.showFallback(
                'No waiver found for this user.',
                'Signed Waiver Preview'
            );
            return;
        }

        if (!waiver.SignedWaiverFileContentInBase64Format) {
            this.showFallback(
                'No signed waiver content available for preview for this user.',
                waiver?.WaiverName || 'Signed Waiver Preview'
            );
            return;
        }

        // Update the currentPreviewWaiver signal so the modal content can access the data
        this.currentPreviewWaiver.set(waiver);

        this.cleanupPdfBlobUrl();
        const blobUrl = this.convertBase64ToPdfUrl(
            waiver.SignedWaiverFileContentInBase64Format
        );

        if (!blobUrl) {
            this.showFallback(
                'Failed to convert PDF content for this user.',
                waiver.WaiverName || 'Signed Waiver Preview'
            );
            return;
        }

        this.pdfBlobUrl.set(blobUrl);
        this.modalTitle.set(waiver.WaiverName || 'Signed Waiver Preview');
        this.showModal.set(true);
    }

    /**
     * Closes the PDF modal and cleans up resources.
     * Overrides the base class method to also clear the current preview waiver.
     */
    override closeModal(): void {
        super.closeModal();
        // Clear the current preview waiver when modal is closed
        this.currentPreviewWaiver.set(null);
    }
}

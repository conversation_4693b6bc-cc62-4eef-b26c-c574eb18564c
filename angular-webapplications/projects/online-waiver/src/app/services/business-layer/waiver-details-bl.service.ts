/**
 * @fileoverview Business layer service for handling waiver details functionality
 * <AUTHOR>
 * @version 1.0.0
 */
import { inject, Injectable } from '@angular/core';
import { RequestState } from 'lib/lib-app-core/src/lib/utils/request-state';
import { TransactionDetailsDL } from 'lib-app-core';
import { TransactionResponse } from '../../interface/transaction.interface';
import {
    GetHtmlWaiverPayload,
    GetHtmlWaiverQueryParams,
    GetPreviewHtmlWaiverQueryParams,
    WaiverInfo,
    WaiverSetContainerDTO,
    WaiverSetResponse,
    WaiverContainerDTO,
    WaiverDataWithInfo,
} from '../../interface/waiver.interface';
import { Router } from '@angular/router';
import { areEqual, SiteContextService, CookieService } from 'lib-app-core';
import { BehaviorSubject, map, Observable } from 'rxjs';
import { WaiverSetDL } from '../data-layer/waiver-set-dl.service';
import { TransactionTimeDL } from 'lib-app-core';
import { SiteViewsServiceBL } from 'lib/lib-app-core/src/lib/services/business-layer/site-views-bl.service';
import { GetHtmlWaiverDL } from '../data-layer/get-html-waiver-dl.service';
import { CreateDefaultCustomerDL } from '../data-layer/create-default-customer-dl.service';
import { PreviewWaiverSetDL } from '../data-layer/preview-waiver-set-dl.service';
import { GetPreviewHtmlWaiverDL } from '../data-layer/get-preview-html-waiver-dl.service';

@Injectable({
    providedIn: 'root',
})
export class WaiverDetailsServiceBL extends RequestState {
    private _transactionData = inject(TransactionDetailsDL);
    private _router = inject(Router);
    private waiverInfoSubject = new BehaviorSubject<WaiverInfo | null>(null);
    private _getAllWaiverSetDL = inject(WaiverSetDL);
    private _previewWaiverSetDL = inject(PreviewWaiverSetDL);
    private _transactionTimeDL = inject(TransactionTimeDL);
    private _siteViewsServiceBL = inject(SiteViewsServiceBL);
    private _createDefaultCustomerDL = inject(CreateDefaultCustomerDL);
    private _getHtmlWaiverDL = inject(GetHtmlWaiverDL);
    private _getPreviewHtmlWaiverDL = inject(GetPreviewHtmlWaiverDL);
    private _cookieService = inject(CookieService);
    waiverInfo$ = this.waiverInfoSubject.asObservable();
    private readonly _siteContextService = inject(SiteContextService);
    getTransactionData(guid: string) {
        this._transactionData.buildApiParams({
            guid,
        });
        return this._transactionData.load();
    }

    /**
     * Extracts waiver information from transaction response
     *
     * This method processes transaction data to find the first waiver set that requires signing.
     * It returns only the first matching waiver to maintain the original single-waiver behavior.
     *
     * @param response - Transaction response containing waiver data
     * @returns Single waiver info object or null if no waivers found
     */
    extractWaiverInfo(response: TransactionResponse): WaiverInfo | null {
        // Find the first transaction line that requires waiver signing
        const matchingLine = response.data
            .flatMap((transaction) => transaction.TransactionLinesDTOList)
            .find(
                (line) =>
                    line.IsWaiverSignRequired === 'Y' &&
                    line.WaiverSignedDTOList
            );

        if (!matchingLine?.WaiverSignedDTOList?.[0]) {
            return null;
        }

        // Extract waiver info from the first matching line
        const waiverSigned = matchingLine.WaiverSignedDTOList[0];
        return {
            waiverSetId: waiverSigned.WaiverSetId,
            siteId: waiverSigned.Site_id,
            productName: matchingLine.ProductName,
            transactionId: matchingLine.TransactionId,
        };
    }

    /**
     * Checks if the site ID in the waiver info matches the site ID in the cookies
     * @param waiverInfo - The waiver info object
     * @returns The waiver info object if the site ID matches, otherwise null
     */
    checkSiteIdMatch(waiverInfo: WaiverInfo | null): WaiverInfo | null {
        if (!waiverInfo) {
            this._router.navigate(['/auth/register']);
            return null;
        }

        const siteId = this._siteContextService.siteId;

        if (areEqual(waiverInfo.siteId, siteId)) {
            this._router.navigate(['/auth/register']);
            return waiverInfo;
        }

        this._siteViewsServiceBL.selectSite(waiverInfo.siteId);
        return waiverInfo;
    }

    /**
     * Sets the waiver info in the subject
     * @param info - The waiver info object
     */
    setWaiverInfo(info: WaiverInfo | null) {
        this.waiverInfoSubject.next(info);
    }

    /**
     * Gets the current waiver info value
     * @returns The current waiver info or null
     */
    getCurrentWaiverInfo(): WaiverInfo | null {
        const currentValue = this.waiverInfoSubject.value;
        return currentValue;
    }

    /**
     * Checks if waiver info is available
     * @returns true if waiver info exists, false otherwise
     */
    hasWaiverInfo(): boolean {
        return this.waiverInfoSubject.value !== null;
    }

    /**
     * Clears the waiver info
     */
    clearWaiverInfo(): void {
        this.waiverInfoSubject.next(null);
    }

    /**
     * Gets all waiver sets for a given language ID and site ID
     * @returns The waiver sets
     */
    getWaiverSet() {
        const siteId = this._siteContextService.siteId.toString();

        const languageId = this._siteContextService.languageId;

        if (!siteId || !languageId) {
            throw new Error(
                'Missing required parameters: siteId or languageId'
            );
        }

        this._previewWaiverSetDL.buildApiParams(siteId, languageId);
        return this._previewWaiverSetDL.load();
    }

    /**
     * Gets all waiver sets for a given language ID and site ID
     * @returns The waiver sets
     */
    getPreviewWaiverSet() {
        const siteId = this._siteContextService.siteId.toString();

        const languageId = this._siteContextService.languageId;

        if (!siteId || !languageId) {
            throw new Error(
                'Missing required parameters: siteId or languageId'
            );
        }

        this._previewWaiverSetDL.buildApiParams(siteId, languageId);
        return this._previewWaiverSetDL.load();
    }

    /**
     * Get all waiver sets with the same waiverSetId
     * @param waiverSetId - The waiver set ID to filter by
     * @returns Observable of array of WaiverSetContainerDTO
     */
    getAllWaiverSetsByWaiverSetId(
        response: WaiverSetResponse
    ): WaiverSetContainerDTO[] {
        const waiverInfo = this.waiverInfoSubject.value;
        const waiverSetId = waiverInfo?.waiverSetId;

        // Handle case when response is empty or undefined
        if (!response?.data?.WaiverSetContainerDTOList) {
            return [];
        }

        // If no waiverInfo or no waiverSetId, return all waiver sets
        if (!waiverInfo || !waiverSetId) {
            return response.data.WaiverSetContainerDTOList;
        }

        // Filter by waiverSetId
        const filteredWaiverSets =
            response.data.WaiverSetContainerDTOList.filter(
                (waiverSet: WaiverSetContainerDTO) =>
                    areEqual(waiverSet.WaiverSetId, waiverSetId)
            );

        // If no matches found (filtered result is 0), return all objects
        if (filteredWaiverSets.length === 0) {
            return response.data.WaiverSetContainerDTOList;
        }

        return filteredWaiverSets;
    }

    /**
     * Filter waiver sets based on effective dates
     * Checks if each waiver is valid for the current date
     * @param waiverSets - Array of waiver sets to filter
     * @returns Filtered array of valid waiver sets
     */
    filterWaiverSetsByEffectiveDates(
        waiverSets: WaiverSetContainerDTO[]
    ): WaiverSetContainerDTO[] {
        const currentDate = new Date();
        currentDate.setHours(0, 0, 0, 0); // Set to start of day for accurate comparison

        return waiverSets.filter((waiverSet) => {
            // Check if any waiver in the set is currently effective
            return waiverSet.WaiversContainerDTO.some((waiver) => {
                const effectiveDate = new Date(waiver.EffectiveDate);
                effectiveDate.setHours(0, 0, 0, 0);

                // If EffectiveTillDate is null, the waiver is always effective after EffectiveDate
                if (!waiver.EffectiveTillDate) {
                    return currentDate >= effectiveDate;
                }

                const effectiveTillDate = new Date(waiver.EffectiveTillDate);
                effectiveTillDate.setHours(23, 59, 59, 999); // Set to end of day

                // Check if current date is within the effective date range
                return (
                    currentDate >= effectiveDate &&
                    currentDate <= effectiveTillDate
                );
            });
        });
    }

    getTransactionTime(transactionId: number) {
        this._transactionTimeDL.buildApiParams({ transactionId });
        return this._transactionTimeDL.load();
    }

    createDefaultCustomer() {
        return this._createDefaultCustomerDL.load();
    }

    getHtmlWaiver(
        queryParams: GetHtmlWaiverQueryParams,
        payload: GetHtmlWaiverPayload
    ) {
        this._getHtmlWaiverDL.buildQueryParams(queryParams);
        this._getHtmlWaiverDL.buildPayload(payload);
        return this._getHtmlWaiverDL.load();
    }

    getPreviewHtmlWaiver(queryParams: GetPreviewHtmlWaiverQueryParams) {
        this._getPreviewHtmlWaiverDL.buildQueryParams(queryParams);
        return this._getPreviewHtmlWaiverDL.load();
    }

    /**
     * Gets waiver preview data (PDF or HTML) based on waiver set
     * @param waiverSet - The waiver set containing waiver data
     * @returns Observable with waiver preview data
     */
    getWaiverPreviewData(
        waiverSet: WaiverSetContainerDTO
    ): Observable<{ base64Data: string; type: 'html' | 'pdf' }> {
        const waiverSetData = waiverSet.WaiversContainerDTO[0];
        // Check if PDF is available first
        if (waiverSetData?.WaiverPdfBase64) {
            return new Observable((observer) => {
                observer.next({
                    base64Data: waiverSetData.WaiverPdfBase64 || '',
                    type: 'pdf',
                });
                observer.complete();
            });
        }

        // Read site and language from site context
        const siteId = this._siteContextService.siteId.toString();
        const languageId = this._siteContextService.languageId;

        if (!siteId || !languageId) {
            throw new Error(
                'Missing required parameters: siteId or languageId'
            );
        }

        const queryParams: GetPreviewHtmlWaiverQueryParams = {
            siteId: siteId,
            languageId: languageId.toString(),
            waiverSetId: waiverSet.WaiverSetId.toString(),
        };

        return this.getPreviewHtmlWaiver(queryParams).pipe(
            map((response) => {
                // Extract base64 from HTML response
                const base64Data =
                    response.data?.CreateCustomerWaiverDTOList[0]
                        ?.CustomerWaiverHTMLForm || '';
                return {
                    base64Data,
                    type: 'html' as const,
                };
            })
        );
    }

    /**
     * Gets waiver preview data with automatic loading and error management
     * @param waiverSet - The waiver set containing waiver data
     * @param onSuccess - Callback for successful response
     * @param onError - Optional callback for error handling
     */
    getWaiverPreviewDataWithState(
        waiverSet: WaiverSetContainerDTO,
        onSuccess: (data: { base64Data: string; type: 'html' | 'pdf' }) => void,
        onError?: (error: Error) => void
    ) {
        this._handleRequest(
            this.getWaiverPreviewData(waiverSet),
            onSuccess,
            onError
        );
    }

    /**
     * Gets all waivers data for a waiver set
     * @param waiverSet - The waiver set containing waiver data
     * @returns Observable with array of waiver data
     */
    getAllWaiversData(
        waiverSet: WaiverSetContainerDTO
    ): Observable<WaiverDataWithInfo[]> {
        const waivers = waiverSet.WaiversContainerDTO;
        const waiverObservables = waivers.map((waiver) => {
            // Check if PDF is available first
            if (waiver?.WaiverPdfBase64) {
                return new Observable<WaiverDataWithInfo>((observer) => {
                    observer.next({
                        waiver,
                        data: {
                            base64Data: waiver.WaiverPdfBase64 || '',
                            type: 'pdf',
                        },
                    });
                    observer.complete();
                });
            }

            // Get customer ID from cookie
            const customerId = this._cookieService.getCookie('customerId');
            if (!customerId) {
                throw new Error('Customer ID is required');
            }

            const defaultCustomerId = parseInt(customerId);

            const queryParams: GetHtmlWaiverQueryParams = {
                defaultCustomer: defaultCustomerId.toString(),
                waiverSetId: waiverSet.WaiverSetId.toString(),
            };

            const payload: GetHtmlWaiverPayload = {
                Channel: 'WEBSITE',
                SignForCustomersIdList: [defaultCustomerId],
                SignatoryGuestCustomerDTO: {},
                SignForGuestCustomersDTOList: [{ Id: defaultCustomerId }],
            };

            return this.getHtmlWaiver(queryParams, payload).pipe(
                map((response) => {
                    // Extract base64 from HTML response
                    const base64Data =
                        response.data?.CreateCustomerWaiverDTOList[0]
                            ?.CustomerWaiverHTMLForm || '';
                    return {
                        waiver,
                        data: {
                            base64Data,
                            type: 'html' as const,
                        },
                    };
                })
            );
        });

        return new Observable((observer) => {
            const results: WaiverDataWithInfo[] = [];
            let completed = 0;

            waiverObservables.forEach((observable, index) => {
                observable.subscribe({
                    next: (result) => {
                        results[index] = result;
                    },
                    error: (error) => {
                        console.error(`Error loading waiver ${index}:`, error);
                        // Add a placeholder for failed waivers
                        results[index] = {
                            waiver: waivers[index],
                            data: {
                                base64Data: '',
                                type: 'html' as const,
                            },
                        };
                    },
                    complete: () => {
                        completed++;
                        if (completed === waiverObservables.length) {
                            observer.next(results);
                            observer.complete();
                        }
                    },
                });
            });
        });
    }

    /**
     * Gets all waivers data with automatic loading and error management
     * @param waiverSet - The waiver set containing waiver data
     * @param onSuccess - Callback for successful response
     * @param onError - Optional callback for error handling
     */
    getAllWaiversDataWithState(
        waiverSet: WaiverSetContainerDTO,
        onSuccess: (data: WaiverDataWithInfo[]) => void,
        onError?: (error: Error) => void
    ) {
        return this._handleRequest(
            this.getAllWaiversData(waiverSet),
            onSuccess,
            onError
        );
    }

    /**
     * Gets waiver data (PDF or HTML) based on waiver set
     * @param waiverSet - The waiver set containing waiver data
     * @returns Observable with waiver data
     */
    getWaiverData(
        waiverSet: WaiverSetContainerDTO
    ): Observable<{ base64Data: string; type: 'html' | 'pdf' }> {
        // Check if PDF is available first
        const waiverSetData = waiverSet.WaiversContainerDTO[0];
        if (waiverSetData?.WaiverPdfBase64) {
            return new Observable((observer) => {
                observer.next({
                    base64Data: waiverSetData.WaiverPdfBase64 || '',
                    type: 'pdf',
                });
                observer.complete();
            });
        }

        // Get customer ID from cookie
        const customerId = this._cookieService.getCookie('customerId');
        if (!customerId) {
            throw new Error('Customer ID is required');
        }

        const defaultCustomerId = parseInt(customerId);

        const queryParams: GetHtmlWaiverQueryParams = {
            defaultCustomer: defaultCustomerId.toString(),
            waiverSetId: waiverSet.WaiverSetId.toString(),
        };

        const payload: GetHtmlWaiverPayload = {
            Channel: 'WEBSITE',
            SignForCustomersIdList: [defaultCustomerId],
            SignatoryGuestCustomerDTO: {},
            SignForGuestCustomersDTOList: [{ Id: defaultCustomerId }],
        };

        return this.getHtmlWaiver(queryParams, payload).pipe(
            map((response) => {
                // Extract base64 from HTML response
                const base64Data =
                    response.data?.CreateCustomerWaiverDTOList[0]
                        ?.CustomerWaiverHTMLForm || '';
                return {
                    base64Data,
                    type: 'html' as const,
                };
            })
        );
    }

    /**
     * Gets waiver data with automatic loading and error management
     * @param waiverSet - The waiver set containing waiver data
     * @param onSuccess - Callback for successful response
     * @param onError - Optional callback for error handling
     */
    getWaiverDataWithState(
        waiverSet: WaiverSetContainerDTO,
        onSuccess: (data: { base64Data: string; type: 'html' | 'pdf' }) => void,
        onError?: (error: Error) => void
    ) {
        return this._handleRequest(
            this.getWaiverData(waiverSet),
            onSuccess,
            onError
        );
    }
}

/**
 * @fileoverview Service to manage waiver signing state using reactive patterns
 * <AUTHOR>
 * @version 1.0.0
 */
import { Injectable, signal, computed } from '@angular/core';
import {
    BehaviorSubject,
    Observable,
    map,
    catchError,
    of,
    startWith,
} from 'rxjs';
import { Router } from '@angular/router';
import { SignPdfWaiverBLService } from './sign-pdf-waiver-bl.service';
import { WaiverSetContainerDTO } from '../../interface/waiver.interface';

export interface SigningState {
    isSigning: boolean;
    error: string;
    success: boolean;
    data?: any;
    shouldNavigate?: boolean;
}

@Injectable({
    providedIn: 'root',
})
export class WaiverSigningStateService {
    // Reactive state using signals
    private readonly _signingState = signal<SigningState>({
        isSigning: false,
        error: '',
        success: false,
    });

    // Public readonly signals
    readonly signingState = this._signingState.asReadonly();
    readonly isSigning = computed(() => this._signingState().isSigning);
    readonly error = computed(() => this._signingState().error);
    readonly success = computed(() => this._signingState().success);

    // BehaviorSubject for async pipe usage
    readonly signingState$ = new BehaviorSubject<SigningState>({
        isSigning: false,
        error: '',
        success: false,
    });

    constructor(
        private readonly _signPdfWaiverBL: SignPdfWaiverBLService,
        private readonly _router: Router
    ) {}

    /**
     * Signs a PDF waiver using reactive patterns
     */
    signPdfWaiver(
        signatureBase64: string,
        waiverSetId: number,
        waiverSetDetailId: number,
        waiverSet: WaiverSetContainerDTO,
        channel: string = 'Website'
    ): Observable<SigningState> {
        // Update state to signing
        this.updateSigningState({
            isSigning: true,
            error: '',
            success: false,
        });

        // Create reactive signing stream
        return this._signPdfWaiverBL
            .signPdfWaiver(
                signatureBase64,
                waiverSetId,
                waiverSetDetailId,
                waiverSet,
                channel
            )
            .pipe(
                map((response) => {
                    // Check if the response indicates success
                    if (response && response.success !== false) {
                        return {
                            isSigning: false,
                            error: '',
                            success: true,
                            data: response,
                            shouldNavigate: true,
                        };
                    } else {
                        // Handle API response that indicates failure
                        return {
                            isSigning: false,
                            error:
                                response?.error ||
                                'Failed to sign waiver. Please try again.',
                            success: false,
                            data: response,
                        };
                    }
                }),
                catchError((error) => {
                    console.error('Error signing waiver:', error);
                    return of({
                        isSigning: false,
                        error: 'Failed to sign waiver. Please try again.',
                        success: false,
                    });
                })
            );
    }

    /**
     * Updates the signing state
     */
    updateSigningState(state: SigningState): void {
        this._signingState.set(state);
        this.signingState$.next(state);
    }

    /**
     * Clears the signing error
     */
    clearError(): void {
        const currentState = this._signingState();
        this.updateSigningState({
            ...currentState,
            error: '',
        });
    }

    /**
     * Resets the signing state
     */
    reset(): void {
        this.updateSigningState({
            isSigning: false,
            error: '',
            success: false,
        });
    }

    /**
     * Gets the current signing status message
     */
    getStatusMessage(): string {
        const state = this._signingState();

        if (state.isSigning) {
            return 'Signing waiver...';
        }
        if (state.error) {
            return state.error;
        }
        if (state.success) {
            return 'Waiver signed successfully!';
        }
        return '';
    }

    /**
     * Navigates to check-in page
     */
    navigateToCheckIn(): void {
        this._router.navigate(['/waivers/check-in']);
    }

    /**
     * Handles success navigation
     */
    handleSuccessNavigation(): void {
        const state = this._signingState();
        if (state.success && state.shouldNavigate) {
            this.navigateToCheckIn();
        }
    }

    /**
     * Resets the signing state and clears navigation flag
     */
    resetState(): void {
        this.updateSigningState({
            isSigning: false,
            error: '',
            success: false,
            shouldNavigate: false,
        });
    }
}

import { Injectable, inject } from '@angular/core';
import { SignHtmlWaiverDL } from '../data-layer/sign-html-waiver-dl.service';
import { RequestState } from 'lib/lib-app-core/src/lib/utils/request-state';
import {
    SignHTMLWaiverPayload,
    SignHTMLWaiverQueryParams,
} from '../../interface/waiver.interface';
import { SiteContextService } from 'lib-app-core';
import { CustomerRelationBL } from './customer-relation-bl.service';

@Injectable()
export class SignHtmlWaiverBLService extends RequestState {
    private readonly _signHtmlWaiverDL = inject(SignHtmlWaiverDL);
    private readonly _customerRelationBL = inject(CustomerRelationBL);
    private readonly _siteContextService = inject(SiteContextService);

    signHtmlWaiver(
        payload: SignHTMLWaiverPayload,
        queryParams: SignHTMLWaiverQueryParams
    ) {
        this._signHtmlWaiverDL.buildPayload(payload);
        this._signHtmlWaiverDL.buildQueryParams(queryParams);
        return this._signHtmlWaiverDL.load();
    }
}

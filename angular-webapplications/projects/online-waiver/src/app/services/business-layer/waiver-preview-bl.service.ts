/**
 * @fileoverview Business layer service for handling waiver preview functionality
 * <AUTHOR>
 * @version 1.0.0
 */
import { inject, Injectable, PLATFORM_ID } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { RequestState } from 'lib-app-core';
import { WaiverPreviewData } from '../../interface/waiver.interface';
import { Base64PdfBLService } from '../buisiness-layer/base64-pdf-bl.service';
import { ElementRef, ChangeDetectorRef } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

@Injectable()
export class WaiverPreviewBLService extends RequestState {
    private readonly _sanitizer = inject(DomSanitizer);
    private readonly _base64PdfService = inject(Base64PdfBLService);
    private readonly platformId = inject(PLATFORM_ID);

    /**
     * Creates a safe HTML data URL
     */
    createHtmlDataUrl(base64Data: string): SafeResourceUrl {
        const decodedHtml = atob(base64Data);
        const dataUrl = `data:text/html;charset=utf-8,${encodeURIComponent(
            decodedHtml
        )}`;
        return this._sanitizer.bypassSecurityTrustResourceUrl(dataUrl);
    }

    /**
     * Creates a safe PDF data URL for browser viewing
     */
    createPdfDataUrl(base64Data: string): SafeResourceUrl {
        const dataUrl = `data:application/pdf;base64,${base64Data}#toolbar=0&navpanes=0&zoom=90`;
        return this._sanitizer.bypassSecurityTrustResourceUrl(dataUrl);
    }

    /**
     * Creates a safe PDF data URL for embedded viewing
     */
    createEmbeddedPdfDataUrl(base64Data: string): SafeResourceUrl {
        // const dataUrl = `data:application/pdf;base64,${base64Data}#toolbar=0&navpanes=0&scrollbar=0&view=FitH`;
        const dataUrl = `data:application/pdf;base64,${base64Data}#toolbar=0&navpanes=0&zoom=90`;
        return this._sanitizer.bypassSecurityTrustResourceUrl(dataUrl);
    }

    /**
     * Creates an empty safe resource URL
     */
    createEmptyResourceUrl(): SafeResourceUrl {
        return this._sanitizer.bypassSecurityTrustResourceUrl('');
    }

    /**
     * Processes waiver data and returns the appropriate URL
     */
    processWaiverData(data: WaiverPreviewData | null): SafeResourceUrl {
        return data
            ? this.processValidData(data)
            : this.createEmptyResourceUrl();
    }

    /**
     * Processes valid waiver data
     */
    private processValidData(data: WaiverPreviewData): SafeResourceUrl {
        if (data.type === 'html') {
            return this.createHtmlDataUrl(data.base64Data);
        }

        if (data.type === 'pdf') {
            return this.createPdfDataUrl(data.base64Data);
        }

        return this.createEmptyResourceUrl();
    }

    /**
     * Converts base64 PDF data to Uint8Array for PDF.js rendering
     */
    convertBase64ToUint8Array(base64Data: string): Uint8Array {
        const cleanBase64 = base64Data.replace(
            /^data:application\/pdf;base64,/,
            ''
        );

        if (this._base64PdfService.validatePdfContent(base64Data)) {
            const binaryString = atob(cleanBase64);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }
            return bytes;
        }

        throw new Error('Invalid PDF content');
    }
}

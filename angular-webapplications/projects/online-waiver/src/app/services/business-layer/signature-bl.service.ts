import { Injectable, signal } from '@angular/core';
import SignaturePad from 'signature_pad';

@Injectable()
export class SignatureBlService {
    // Internal signals
    readonly isDrawing = signal<boolean>(false);
    readonly hasSignature = signal<boolean>(false);

    private signaturePad: SignaturePad | null = null;

    initializeCanvas(canvas: HTMLCanvasElement): void {
        this.setupCanvas(canvas);
        this.initializeSignaturePad(canvas);
    }

    private setupCanvas(canvas: HTMLCanvasElement): void {
        // Get computed styles to respect Tailwind CSS classes
        const computedStyle = window.getComputedStyle(canvas);
        const width = computedStyle.width;
        const height = computedStyle.height;

        // Set canvas display size based on CSS classes
        canvas.style.width = width;
        canvas.style.height = height;

        // Set canvas internal resolution for crisp rendering
        const rect = canvas.getBoundingClientRect();
        canvas.width = rect.width;
        canvas.height = rect.height;
    }

    private initializeSignaturePad(canvas: HTMLCanvasElement): void {
        this.signaturePad = new SignaturePad(canvas, {
            backgroundColor: 'rgba(255, 255, 255, 0)',
            penColor: '#B0E0E6',
            minWidth: 2,
            maxWidth: 2,
            throttle: 16,
            velocityFilterWeight: 0.7,
        });

        // Listen to signature pad events
        this.signaturePad.addEventListener('beginStroke', () => {
            this.isDrawing.set(true);
            this.hasSignature.set(true);
        });

        this.signaturePad.addEventListener('endStroke', () => {
            this.isDrawing.set(false);
        });
    }

    clearSignature(canvas: HTMLCanvasElement): void {
        if (this.signaturePad) {
            this.signaturePad.clear();
            this.hasSignature.set(false);
        }
    }

    /**
     * Gets the signature as decoded base64 string without the data URL prefix
     * @param canvas - The canvas element
     * @returns The decoded base64 string without "data:image/png;base64," prefix
     */
    getSignatureAsDecodedBase64(canvas: HTMLCanvasElement): string {
        if (!this.hasSignature() || !this.signaturePad) {
            return '';
        }

        // Export with transparency (PNG supports transparency)
        const fullBase64 = this.signaturePad.toDataURL('image/png');
        const decodedBase64 = fullBase64.replace('data:image/png;base64,', '');
        return decodedBase64;
    }
}

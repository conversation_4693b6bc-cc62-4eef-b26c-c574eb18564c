/**
 * @fileoverview CustomerRelationBL is a service that provides business logic for the customer relation
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-30
 */
import { inject, Injectable, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { RequestState } from 'lib/lib-app-core/src/lib/utils/request-state';
import { RelatedCustomerDL } from '../data-layer/related-customer-dl.service';
import { PrimaryCustomerDL } from '../data-layer/primary-customer-dl.service';
import { CookieService } from 'lib/lib-app-core/src/lib/services/cookie-service/cookie.service';
import { PrimaryCustomerDataDTOModel } from '../../models/primary-customer-data.model';
import { CustomerRelationshipDTO } from '../../models/customer-relationship-dto.model';
import { UserLoginDTOModel } from 'lib/lib-auth/src/lib/models/user-login-dto.model';
import {
    BehaviorSubject,
    catchError,
    map,
    Observable,
    of,
    startWith,
    switchMap,
} from 'rxjs';
import {
    CustomerDataResponse,
    ILoadingState,
    IParticipant,
    RelatedCustomerResponse,
} from '../../interface/customer.interface';
import { customerValidation } from '../../constants/customer.constant';

@Injectable({
    providedIn: 'root',
})
export class CustomerRelationBL extends RequestState {
    private _relatedCustomerDL = inject(RelatedCustomerDL);
    private _primaryCustomerDL = inject(PrimaryCustomerDL);
    // Trigger subjects for refetching
    readonly _primaryTrigger = new BehaviorSubject<void>(undefined);
    readonly _minorsTrigger = new BehaviorSubject<void>(undefined);
    private _cookieService = inject(CookieService);

    /**
     * Gets the current customer ID from cookies
     */
    private get customerId(): string {
        const customerId = this._cookieService.getCookie('customerId');
        if (!customerId) {
            throw new Error('Customer ID is required');
        }
        return customerId;
    }

    // Shared observables to prevent duplicate requests
    private _primaryCustomerData$ = this._primaryTrigger.pipe(
        switchMap(() => this.getPrimaryCustomerData())
    );

    private _relatedCustomerData$ = this._minorsTrigger.pipe(
        switchMap(() => this.getRelatedCustomerData())
    );

    // Derived signals from shared observables
    primaryParticipant = toSignal(
        this._primaryCustomerData$.pipe(map((participant) => participant)),
        { initialValue: null }
    );

    relatedParticipants = toSignal(
        this._relatedCustomerData$.pipe(map((participant) => participant)),
        { initialValue: null }
    );

    selectedParticipants = signal<IParticipant[]>([]);

    /**
     * Transforms raw customer data to IParticipant format
     */
    private transformToParticipant(
        customerDTO: UserLoginDTOModel
    ): IParticipant {
        const { FirstName, MiddleName, LastName, DateOfBirth } = customerDTO;
        const fullName = [FirstName, MiddleName, LastName].filter(Boolean);

        return {
            id: customerDTO.Id.toString(),
            email: customerDTO.Email,
            phoneNumber: customerDTO.PhoneNumber,
            firstName: fullName[0] ?? 'Unknown',
            lastName: fullName.slice(1).join(' '),
            dateOfBirth: DateOfBirth || '',
            type: 'primary',
        };
    }

    /**
     * Transforms related customer data to IParticipant format
     */
    private transformRelatedCustomer(
        raw: CustomerRelationshipDTO
    ): IParticipant {
        const dto = CustomerRelationshipDTO.fromSingle(raw);
        const [firstName, ...rest] = (dto.RelatedCustomerName ?? '').split(' ');

        return {
            id: dto.RelatedCustomerDTO?.Id.toString(),
            email: dto.RelatedCustomerDTO?.Email,
            phoneNumber: dto.RelatedCustomerDTO?.PhoneNumber,
            firstName: firstName || 'Unknown',
            lastName: rest.join(' '),
            dateOfBirth: dto.RelatedCustomerDTO?.DateOfBirth || '',
            type: 'minor',
        };
    }

    /**
     * Creates error state for failed requests
     */
    private createErrorState(message: string): ILoadingState<never> {
        return {
            loading: false,
            data: null,
            error: message,
        };
    }

    /**
     * Gets the primary customer data
     *
     * This method is used to get the primary customer data from the database
     *
     * @returns Observable<ILoadingState<IParticipant>> - The primary customer data
     */
    getPrimaryCustomerData(): Observable<ILoadingState<IParticipant>> {
        this._primaryCustomerDL.buildApiParams({ customerId: this.customerId });

        return this._primaryCustomerDL.load().pipe(
            startWith({
                loading: true,
                data: null,
                error: null,
            } as ILoadingState<IParticipant>),
            map(
                (
                    response: CustomerDataResponse | ILoadingState<IParticipant>
                ): ILoadingState<IParticipant> => {
                    if ('loading' in response && response.loading) {
                        return response;
                    }

                    if (Array.isArray(response.data)) {
                        const rawData = response.data[0];
                        if (!rawData) {
                            return this.createErrorState(
                                customerValidation.NO_PRIMARY_CUSTOMER_DATA
                            );
                        }

                        //Type guard to ensure response is CustomerDataResponse
                        if ('totalPages' in response) {
                            const customerDTO =
                                PrimaryCustomerDataDTOModel.fromSingle({
                                    data: [rawData],
                                    customersImage: response.customersImage,
                                    customersIdImage: response.customersIdImage,
                                    totalPages: response.totalPages,
                                });

                            return {
                                loading: false,
                                data: this.transformToParticipant(
                                    customerDTO.data[0]
                                ),
                                error: null,
                            };
                        }
                    }

                    return this.createErrorState(
                        customerValidation.CUSTOMER_RELATION_ERROR
                    );
                }
            ),
            catchError((error: unknown) => {
                console.error(
                    customerValidation.ERROR_LOADING_PRIMARY_CUSTOMER_DATA,
                    error
                );
                return of(
                    this.createErrorState(
                        customerValidation.ERROR_LOADING_PRIMARY_CUSTOMER_DATA
                    )
                );
            })
        );
    }

    /**
     * Gets the related customer data
     *
     * This method is used to get the related customer data from the database
     *
     * @returns Observable<ILoadingState<IParticipant[]>> - The related customer data
     */
    getRelatedCustomerData(): Observable<ILoadingState<IParticipant[]>> {
        this._relatedCustomerDL.buildApiParams({ customerId: this.customerId });

        return this._relatedCustomerDL.load().pipe(
            startWith({
                loading: true,
                data: null,
                error: null,
            } as ILoadingState<IParticipant[]>),
            map(
                (
                    response:
                        | RelatedCustomerResponse
                        | ILoadingState<IParticipant[]>
                ): ILoadingState<IParticipant[]> => {
                    if ('loading' in response && response.loading) {
                        return response;
                    }

                    if ('CustomerRelationshipDTO' in response) {
                        const rawList = response.CustomerRelationshipDTO ?? [];
                        const participants = rawList.map((raw) =>
                            this.transformRelatedCustomer(raw)
                        );

                        return {
                            loading: false,
                            data: participants,
                            error: null,
                        };
                    }

                    return this.createErrorState(
                        customerValidation.CUSTOMER_RELATION_ERROR
                    );
                }
            ),
            catchError((error: unknown) => {
                console.error(
                    customerValidation.ERROR_LOADING_RELATED_CUSTOMER_DATA,
                    error
                );
                return of(
                    this.createErrorState(
                        customerValidation.ERROR_LOADING_RELATED_CUSTOMER_DATA
                    )
                );
            })
        );
    }

    refetchPrimaryCustomerData(): void {
        this._primaryTrigger.next();
    }

    refetchRelatedCustomerData(): void {
        this._minorsTrigger.next();
    }

    /**
     * Gets both primary and related customer data in raw format
     * Simple BL function that returns both responses together
     *
     * @returns Observable<{primary: CustomerDataResponse, related: RelatedCustomerResponse}> - Both complete responses
     */
    getCustomerRawData(): Observable<{
        primary: CustomerDataResponse;
        related: RelatedCustomerResponse;
    }> {
        this._primaryCustomerDL.buildApiParams({ customerId: this.customerId });
        this._relatedCustomerDL.buildApiParams({ customerId: this.customerId });

        return this._primaryCustomerDL.load().pipe(
            switchMap((primaryResponse: CustomerDataResponse) => {
                return this._relatedCustomerDL.load().pipe(
                    map((relatedResponse: RelatedCustomerResponse) => ({
                        primary: primaryResponse,
                        related: relatedResponse,
                    }))
                );
            })
        );
    }
}

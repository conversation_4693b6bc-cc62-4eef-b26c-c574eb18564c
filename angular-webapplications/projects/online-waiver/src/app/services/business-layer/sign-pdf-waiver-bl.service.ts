/**
 * @fileoverview Business layer service for building sign PDF waiver payload
 * <AUTHOR>
 * @version 1.0.0
 */
import { Injectable, inject, signal } from '@angular/core';
import { Observable, map, switchMap, catchError, of } from 'rxjs';
import { SignPdfWaiverDL } from '../data-layer/sign-pdf-waiver-dl.service';
import { CustomerRelationBL } from './customer-relation-bl.service';
import { SiteContextService } from 'lib-app-core';
import {
    SignPDFWaiverPayload,
    SignForCustomerDTO,
    CustomerContentDTO,
    WaiversDTO,
    SignatoryCustomerDTO,
    CustIdSignatureImageBase64DTO,
    WaiverSetContainerDTO,
} from '../../interface/waiver.interface';
import { RequestState } from 'lib/lib-app-core/src/lib/utils/request-state';
import { CustomerDTO } from '../../models/customer-data-dto.model';
import { UserLoginDTOModel } from 'lib/lib-auth/src/lib/models/user-login-dto.model';

@Injectable()
export class SignPdfWaiverBLService extends RequestState {
    private readonly _signPdfWaiverDL = inject(SignPdfWaiverDL);
    private readonly _customerRelationBL = inject(CustomerRelationBL);
    private readonly _siteContextService = inject(SiteContextService);

    // Reactive state management - only keep what's needed
    private readonly _primaryCustomer = signal<UserLoginDTOModel | null>(null);
    private readonly _participants = signal<CustomerDTO[]>([]);

    /**
     * Signs a PDF waiver with the provided signature data
     * @param signatureBase64 - The signature in base64 format
     * @param waiverSetId - The waiver set ID
     * @param waiverSetDetailId - The waiver set detail ID
     * @param waiverSet - The complete waiver set data for extracting metadata
     * @param channel - The channel (e.g., 'WEB')
     * @returns Observable with the sign response
     */
    signPdfWaiver(
        signatureBase64: string,
        waiverSetId: number,
        waiverSetDetailId: number,
        waiverSet: WaiverSetContainerDTO,
        channel: string = ''
    ): Observable<any> {
        // Load customer data if not already loaded
        return this.loadCustomerData().pipe(
            switchMap(() => {
                const payload = this.buildSignPdfWaiverPayload(
                    signatureBase64,
                    waiverSetId,
                    waiverSetDetailId,
                    waiverSet,
                    channel
                );

                this._signPdfWaiverDL.buildApiParams(payload);
                return this._signPdfWaiverDL.load();
            })
        );
    }

    /**
     * Loads customer data from the customer relation service
     */
    private loadCustomerData(): Observable<void> {
        return this._customerRelationBL.getCustomerRawData().pipe(
            map((response) => {
                const { primary, related } = response;

                // Set primary customer (first customer in the array)
                if (primary.data && primary.data.length > 0) {
                    this._primaryCustomer.set(primary.data[0]);
                }

                // Get selected participants instead of all related customers
                const selectedParticipants =
                    this._customerRelationBL.selectedParticipants();

                // Transform selected participants to CustomerDTO format
                if (selectedParticipants.length > 0) {
                    const participants = selectedParticipants.map(
                        (participant) => {
                            // Find the corresponding CustomerDTO from related customers
                            const relatedCustomer =
                                related.CustomerRelationshipDTO?.find(
                                    (rel) =>
                                        rel.RelatedCustomerDTO?.Id.toString() ===
                                        participant.id
                                )?.RelatedCustomerDTO;

                            if (relatedCustomer) {
                                return relatedCustomer;
                            }

                            // Fallback: create a basic CustomerDTO from participant data
                            const customerDTO = new CustomerDTO();
                            customerDTO.Id = parseInt(participant.id);
                            customerDTO.FirstName = participant.firstName;
                            customerDTO.LastName = participant.lastName;
                            customerDTO.Email = participant.email;
                            customerDTO.PhoneNumber = participant.phoneNumber;
                            customerDTO.DateOfBirth = participant.dateOfBirth;
                            customerDTO.SiteId =
                                this._siteContextService.siteId;
                            return customerDTO;
                        }
                    );

                    this._participants.set(participants);
                } else {
                    // If no participants selected, use empty array
                    this._participants.set([]);
                }
            }),
            catchError((error) => {
                console.error('Error loading customer data:', error);
                return of(void 0);
            })
        );
    }

    /**
     * Constructs the data transfer object (DTO) required for submitting a signed PDF waiver.
     */
    private buildSignPdfWaiverPayload(
        signatureBase64: string,
        waiverSetId: number,
        waiverSetDetailId: number,
        waiverSet: WaiverSetContainerDTO,
        channel: string
    ): SignPDFWaiverPayload {
        const primaryCustomer = this._primaryCustomer();
        const participants = this._participants();
        if (!primaryCustomer) {
            throw new Error('Primary customer data is required');
        }

        // Create the main waiver DTO
        const waiverBaseDTO: SignPDFWaiverPayload = {
            SignForCustomerDTOList: [],
            CustomerContentDTOList: [],
            WaiversDTO: {} as WaiversDTO,
            SignatoryCustomerDTO: {} as SignatoryCustomerDTO,
            CustIdSignatureImageBase64List: [],
            Channel: channel,
        };

        // Build WaiversDTO using actual waiver set data
        const waiverDTO: WaiversDTO = {
            waiverSetId: waiverSetId,
            waiverSetDetailId: waiverSetDetailId,
            name: this.getWaiverName(waiverSet),
            waiverFileName: this.getWaiverFileName(waiverSet),
            isActive: true,
            validForDays: this.getWaiverValidForDays(waiverSet),
        };
        waiverBaseDTO.WaiversDTO = waiverDTO;

        // Set primary customer details
        const primaryCustomerDTO =
            this.buildPrimaryCustomerDTO(primaryCustomer);
        waiverBaseDTO.SignatoryCustomerDTO = primaryCustomerDTO;

        // Check if primary customer is present in participants list
        const primaryCustomerInParticipants = participants.some(
            (participant) => participant.Id === primaryCustomer.Id
        );

        // If primary customer is in participants, push them first
        if (primaryCustomerInParticipants) {
            waiverBaseDTO.SignForCustomerDTOList.push(primaryCustomerDTO);
        }

        // Add remaining participants (excluding primary customer if they were already added)
        if (participants.length > 0) {
            const filteredParticipants = primaryCustomerInParticipants
                ? participants.filter(
                      (participant) => participant.Id !== primaryCustomer.Id
                  )
                : participants;

            if (filteredParticipants.length > 0) {
                const participantsList =
                    this.buildParticipantsDTOList(filteredParticipants);
                waiverBaseDTO.SignForCustomerDTOList.push(...participantsList);
            }
        }

        // Build signature list
        waiverBaseDTO.CustIdSignatureImageBase64List = this.buildSignatureList(
            primaryCustomer,
            signatureBase64
        );

        // Build customer content DTO list
        waiverBaseDTO.CustomerContentDTOList = this.buildCustomerContentDTOList(
            primaryCustomer,
            participants
        );

        return waiverBaseDTO;
    }

    /**
     * Builds the primary customer DTO from user login data
     */
    private buildPrimaryCustomerDTO(
        primaryCustomer: UserLoginDTOModel
    ): SignatoryCustomerDTO {
        const customerDTO = new CustomerDTO();

        // Directly assign all properties from primaryCustomer since it has everything
        Object.assign(customerDTO, primaryCustomer);

        // Just ensure SiteId is set correctly
        customerDTO.SiteId = this._siteContextService.siteId;

        return customerDTO;
    }

    /**
     * Builds the participants DTO list
     */
    private buildParticipantsDTOList(
        participants: CustomerDTO[]
    ): SignForCustomerDTO[] {
        return participants.map((participant) => {
            const customerDTO = new CustomerDTO();
            Object.assign(customerDTO, participant);
            return customerDTO;
        });
    }

    /**
     * Builds the signature list based on customer data
     */
    private buildSignatureList(
        primaryCustomer: UserLoginDTOModel,
        signatureBase64: string
    ): CustIdSignatureImageBase64DTO[] {
        return [
            {
                Key: primaryCustomer.Id,
                Value: signatureBase64,
            },
        ];
    }

    /**
     * Builds the customer content DTO list
     */
    private buildCustomerContentDTOList(
        primaryCustomer: UserLoginDTOModel,
        participants: CustomerDTO[]
    ): CustomerContentDTO[] {
        const contentList: CustomerContentDTO[] = [];

        // Check if primary customer is already in the participants list
        const primaryCustomerInParticipants = participants.some(
            (participant) => participant.Id === primaryCustomer.Id
        );

        // Add primary customer content only if it's not already in participants
        if (!primaryCustomerInParticipants) {
            // Get email from contact list for primary customer
            const primaryEmailContact = primaryCustomer.ContactDTOList?.find(
                (contact) => contact.ContactType === 1
            );
            const primaryEmail = primaryEmailContact?.Attribute1 || '';

            contentList.push({
                CustomerId: primaryCustomer.Id,
                CustomerName:
                    `${primaryCustomer.FirstName} ${primaryCustomer.LastName}`.trim(),
                PhoneNumber: primaryCustomer.PhoneNumber || '',
                EmailId: primaryEmail,
                CustomerDOB: primaryCustomer.DateOfBirth || '',
                Attribute1Name: '',
                Attribute2Name: '',
                WaiverCustomAttributeList: [],
            });
        }

        // Add participant content
        participants.forEach((participant) => {
            contentList.push({
                CustomerId: participant.Id,
                CustomerName:
                    `${participant.FirstName} ${participant.LastName}`.trim(),
                PhoneNumber: participant.PhoneNumber || '',
                EmailId: participant.Email || '',
                CustomerDOB: participant.DateOfBirth || '',
                Attribute1Name: '',
                Attribute2Name: '',
                WaiverCustomAttributeList: [],
            });
        });

        return contentList;
    }

    /**
     * Gets the waiver name from waiver set data
     */
    private getWaiverName(waiverSet: WaiverSetContainerDTO): string {
        return waiverSet?.Name || 'Waiver Name';
    }

    /**
     * Gets the waiver file name from waiver set data
     */
    private getWaiverFileName(waiverSet: WaiverSetContainerDTO): string {
        return (
            waiverSet?.WaiversContainerDTO?.[0]?.WaiverFileName || 'waiver.pdf'
        );
    }

    /**
     * Gets the waiver valid for days from waiver set data
     */
    private getWaiverValidForDays(
        waiverSet: WaiverSetContainerDTO
    ): number | null {
        return waiverSet?.WaiversContainerDTO?.[0]?.ValidForDays || null;
    }
}

/**
 * @fileoverview HTML Waiver Signing Business Layer Service
 * @description Handles business logic for HTML waiver signing including signature extraction,
 * customer data management, and API communication for HTML waiver submission.
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-XX
 */
import {
    Injectable,
    signal,
    computed,
    inject,
    PLATFORM_ID,
} from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { isPlatformBrowser } from '@angular/common';
import { SignHtmlWaiverBLService } from './sign-html-waiver-bl.service';
import { CustomerRelationBL } from './customer-relation-bl.service';
import { SignHTMLWaiverPayload } from '../../interface/waiver.interface';
import { CookieService } from 'lib-app-core';
import { firstValueFrom } from 'rxjs';

import {
    HtmlWaiverData,
    HtmlWaiverCallbacks,
    HtmlWaiverSigningData,
} from '../../interface/pdf-waiver-content.interface';

/**
 * Business Layer Service for HTML Waiver Signing
 *
 * This service manages the complete HTML waiver signing workflow:
 * - State management for HTML waiver signing process
 * - HTML content processing and script injection
 * - Signature capture and processing
 * - Integration with SignHtmlWaiverBLService for API calls
 * - Navigation and callback handling
 */
@Injectable({
    providedIn: 'root',
})
export class HtmlWaiverSigningBLService {
    // Dependency injections
    private readonly _signHtmlWaiverBL = inject(SignHtmlWaiverBLService);
    private readonly _customerRelationBL = inject(CustomerRelationBL);
    private readonly _cookieService = inject(CookieService);
    private readonly _sanitizer = inject(DomSanitizer);
    private readonly platformId = inject(PLATFORM_ID);

    // State signals
    private readonly _isSigningInProgress = signal<boolean>(false);
    private readonly _currentWaiverContent = signal<string>('');
    private readonly _waivers = signal<HtmlWaiverData[]>([]);
    private readonly _signingData = signal<HtmlWaiverSigningData | null>(null);
    private readonly _callbacks = signal<HtmlWaiverCallbacks | null>(null);
    private readonly _relatedCustomerIds = signal<number[]>([]);

    // Computed properties
    readonly isHtmlWaiverSigningInProgress = computed(() =>
        this._isSigningInProgress()
    );
    readonly getCurrentHtmlWaiverContent = computed(() =>
        this._currentWaiverContent()
    );

    constructor() {
        this.setupGlobalNavigation();
    }

    /**
     * Initializes HTML waiver signing with provided data and callbacks
     */
    initializeHtmlWaiverSigning(
        waivers: HtmlWaiverData[],
        signingData: HtmlWaiverSigningData,
        callbacks: HtmlWaiverCallbacks
    ): void {
        this._waivers.set(waivers);
        this._signingData.set(signingData);
        this._callbacks.set(callbacks);
    }

    /**
     * Starts the HTML waiver signing process
     */
    async startHtmlWaiverSigning(): Promise<void> {
        this._isSigningInProgress.set(true);
        await this.loadSelectedParticipantIds();
        this.loadCurrentHtmlWaiver();
    }

    /**
     * Resets HTML waiver signing state
     */
    resetHtmlWaiverSigning(): void {
        this._isSigningInProgress.set(false);
        this._currentWaiverContent.set('');
        this._waivers.set([]);
        this._signingData.set(null);
        this._callbacks.set(null);
        this._relatedCustomerIds.set([]);

        // Clean up global methods (only in browser)
        if (isPlatformBrowser(this.platformId)) {
            delete (window as any).angularNavigateByUrl;
            delete (window as any).angularCaptureSignature;
            delete (window as any).revealSignPad;
        }
    }

    /**
     * Loads selected participant IDs from the customer relation service
     */
    private async loadSelectedParticipantIds(): Promise<void> {
        try {
            // Get selected participants instead of all related customers
            const selectedParticipants =
                this._customerRelationBL.selectedParticipants();

            if (selectedParticipants.length > 0) {
                const selectedCustomerIds = selectedParticipants
                    .map((participant) => parseInt(participant.id, 10))
                    .filter((id) => !isNaN(id));

                this._relatedCustomerIds.set(selectedCustomerIds);
            } else {
                // If no participants selected, use empty array
                this._relatedCustomerIds.set([]);
            }
        } catch (error) {
            console.error('Error loading selected participant data:', error);
            this._relatedCustomerIds.set([]);
        }
    }

    /**
     * Loads the current HTML waiver content
     */
    private loadCurrentHtmlWaiver(): void {
        const waivers = this._waivers();
        if (waivers.length === 0) return;

        const currentWaiver = waivers[0];
        try {
            const decodedHtml = atob(currentWaiver.CustomerWaiverHTMLForm);
            this._currentWaiverContent.set(decodedHtml);

            // Inject scripts after a small delay to ensure DOM is ready
            setTimeout(() => {
                this.injectScripts(decodedHtml);
            }, 100);
        } catch (error) {
            console.error('Error decoding HTML waiver content:', error);
            this.handleError('Failed to load HTML waiver content');
        }
    }

    /**
     * Injects scripts from HTML content into the DOM
     */
    private injectScripts(htmlContent: string): void {
        const waiverContainer = document.getElementById('waiverhtml');
        const scriptsContainer = document.getElementById('waiver-scripts');

        if (!waiverContainer) {
            console.error('Waiver container not found');
            return;
        }

        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = htmlContent;

        const scripts = tempDiv.querySelectorAll('script');

        // Clear any existing scripts
        if (scriptsContainer) {
            scriptsContainer.innerHTML = '';
        }

        scripts.forEach((script) => {
            // Create a new script element
            const newScript = document.createElement('script');

            // Copy script attributes
            Array.from(script.attributes).forEach((attr) => {
                newScript.setAttribute(attr.name, attr.value);
            });

            // Copy script content
            newScript.textContent = script.textContent;

            // Append to scripts container or waiver container
            const targetContainer = scriptsContainer || waiverContainer;
            targetContainer.appendChild(newScript);

            // Execute the script immediately using Function constructor
            if (newScript.textContent) {
                try {
                    const scriptFunction = new Function(newScript.textContent);
                    scriptFunction();
                } catch (error) {
                    console.error('Error executing injected script:', error);
                }
            }
        });
    }

    /**
     * Extracts signature data from URL parameters
     */
    private extractSignatureFromUrl(url: string): string {
        try {
            const urlObj = new URL(url);
            return urlObj.searchParams.get('data') || '';
        } catch (error) {
            console.error('Error extracting signature from URL:', error);
            return '';
        }
    }

    /**
     * Processes HTML waiver signature and submits to API
     */
    private async processHtmlWaiverSignature(
        signatureData: string
    ): Promise<void> {
        const signingData = this._signingData();
        if (!signingData) {
            this.handleError('No signing data available');
            return;
        }

        if (!signatureData?.trim()) {
            this.handleError('Signature data is required');
            return;
        }

        try {
            const payload: SignHTMLWaiverPayload = {
                WaiverSetId: signingData.waiverSetId,
                Channel: 'WEBSITE',
                ApprovingManagerId: parseInt(
                    this._cookieService.getCookie('userRoleId') ?? '0'
                ),
                SignatoryGuestCustomerDTO: {},
                CreateCustomerSignedWaiverDTOList: [
                    {
                        WaiverSetDetailId: signingData.waiverSetDetailId,
                        SignForCustomersIdList: [...this._relatedCustomerIds()],
                        DocumentIdentifier: this.generateDocumentIdentifier(),
                        SignaturesAndAcceptanceEncoded: signatureData,
                        SignForGuestCustomersDTOList: [],
                    },
                ],
            };

            const response = await firstValueFrom(
                this._signHtmlWaiverBL.signHtmlWaiver(payload, {
                    CustomerId: signingData.customerId.toString(),
                    WaiverSetId: signingData.waiverSetId.toString(),
                })
            );

            if (response?.data) {
                this.handleSuccess(response.data);
            } else {
                this.handleError('Failed to sign HTML waiver');
            }
        } catch (error) {
            console.error('Error processing HTML waiver signature:', error);
            this.handleError('An error occurred while signing the waiver');
        }
    }

    /**
     * Sets up global navigation methods for HTML waiver communication
     */
    private setupGlobalNavigation(): void {
        if (!isPlatformBrowser(this.platformId)) {
            return;
        }

        (window as any).angularNavigateByUrl = (url: string) => {
            const signatureData = this.extractSignatureFromUrl(url);

            if (signatureData) {
                this.processHtmlWaiverSignature(signatureData);
            } else if (url.includes('success')) {
                this.handleSuccess();
            } else if (url.includes('cancel')) {
                this.handleCancel();
            }
        };

        (window as any).angularCaptureSignature = (signatureData: string) => {
            this.processHtmlWaiverSignature(signatureData);
        };
    }

    /**
     * Handles successful HTML waiver completion
     */
    private handleSuccess(waiverCode?: string): void {
        const callbacks = this._callbacks();
        callbacks?.onSuccess(waiverCode);
        this.resetHtmlWaiverSigning();
    }

    /**
     * Handles HTML waiver cancellation
     */
    private handleCancel(): void {
        const callbacks = this._callbacks();
        callbacks?.onCancel();
        this.resetHtmlWaiverSigning();
    }

    /**
     * Handles HTML waiver errors
     */
    private handleError(error: string): void {
        const callbacks = this._callbacks();
        callbacks?.onError(error);
        this.resetHtmlWaiverSigning();
    }

    /**
     * Generates a unique document identifier
     */
    private generateDocumentIdentifier(): string {
        const chars = '0123456789abcdefghijklmnopqrstuvwxyz';
        const segments = [8, 4, 4, 4, 12]; // UUID format: 8-4-4-4-12

        return segments
            .map((length) => {
                let segment = '';
                for (let i = 0; i < length; i++) {
                    segment += chars.charAt(
                        Math.floor(Math.random() * chars.length)
                    );
                }
                return segment;
            })
            .join('-');
    }
}

/**
 * @fileoverview Data layer service for handling change password API calls
 * <AUTHOR>
 * @version 1.0.0
 */

import { inject, Injectable } from '@angular/core';
import {
    ApiServiceBase,
    DEFAULT_APP_CONFIG_TOKEN,
    SiteContextService,
} from 'lib-app-core';
import { Observable, switchMap } from 'rxjs';
import {
    ChangePasswordEncryptionConfig,
    ChangePasswordEncryptionService,
} from 'lib/lib-auth/src/lib/services/change-password-encryption.service';
import { SignWaiverService } from '../../core/sign-waiver.service';

export interface ChangePasswordRequest {
    currentPassword: string;
    newPassword: string;
}

export interface ChangePasswordResponse {
    success: boolean;
    message: string;
}

@Injectable()
export class ChangePasswordDL extends ApiServiceBase {
    private _requestPayload!: ChangePasswordRequest;
    private readonly changePasswordEncryptionService = inject(
        ChangePasswordEncryptionService
    );
    private readonly siteContextService = inject(SiteContextService);
    private readonly signWaiverService = inject(SignWaiverService);

    constructor() {
        super('change_password_data', 'CHANGE_PASSWORD');
        this.init();
    }

    /**
     * Builds the API parameters for the change password request
     * @param data - The change password request data
     */
    buildApiParams(data: ChangePasswordRequest) {
        this._requestPayload = data;
    }

    /**
     * Executes the change password API call with encrypted credentials
     *
     * Process:
     * 1. Gets the current user ID from SiteContextService
     * 2. Fetches customer details using SignWaiverService to get the email (UserName field)
     * 3. Constructs the API URL for change password endpoint
     * 4. Creates encryption configuration with user credentials and app metadata
     * 5. Encrypts the change password data using hybrid encryption (AES + RSA)
     * 6. Sends encrypted payload to the server
     *
     * @returns Observable of the server response containing change password result
     */
    load(): Observable<ChangePasswordResponse> {
        const url = `${this.getApiUrl()}`;
        const userId = this.siteContextService.userId;

        if (!userId) {
            throw new Error('User ID not found in site context');
        }

        // Get customer details to fetch the email
        return this.signWaiverService.getCustomerById(parseInt(userId)).pipe(
            switchMap((customerResponse: any) => {
                // Extract UserName from the customer response
                const customerData = customerResponse?.data?.[0];
                const userName =
                    customerData?.UserName ||
                    customerData?.ProfileDTO?.UserName;

                if (!userName) {
                    throw new Error('Customer email (UserName) not found');
                }

                // Create encryption config using the ChangePasswordEncryptionConfig interface
                const encryptionConfig: ChangePasswordEncryptionConfig = {
                    siteId: this.siteContextService.siteId,
                    applicationName: this.siteContextService.applicationName,
                    applicationVersion: this.siteContextService.applicationVersion,
                    applicationIdentifier: this.siteContextService.applicationIdentifier,
                    userName: userName, // Customer's email from the API response
                    currentPassword: this._requestPayload.currentPassword,
                    newPassword: this._requestPayload.newPassword,
                };

                return this.changePasswordEncryptionService
                    .encrypt(encryptionConfig)
                    .pipe(
                        switchMap((encryptedPayload) =>
                            this._http.post<ChangePasswordResponse>(
                                url,
                                encryptedPayload
                            )
                        )
                    );
            })
        );
    }
}

/**
 * @fileoverview Data layer service for handling sign HTML waiver API calls
 * <AUTHOR>
 * @version 1.0.0
 */
import { Injectable } from '@angular/core';
import { ApiServiceBase } from 'lib-app-core';
import { Observable } from 'rxjs';
import {
    SignHTMLWaiverPayload,
    SignHTMLWaiverResponse,
    SignHTMLWaiverQueryParams,
} from '../../interface/waiver.interface';

@Injectable()
export class SignHtmlWaiverDL extends ApiServiceBase {
    private _payload!: SignHTMLWaiverPayload;
    private _queryParams!: SignHTMLWaiverQueryParams;

    constructor() {
        super('sign_html_waiver', 'POST_HTML_WAIVER');
        this.init();
    }
    /**
     * Builds the query parameters for the API request
     * @param data - Query parameters for the request
     */
    buildQueryParams(data: SignHTMLWaiverQueryParams) {
        this._queryParams = data;
    }

    /**
     * Builds the payload for the API request
     * @param data - Payload data for the request
     */
    buildPayload(data: SignHTMLWaiverPayload) {
        this._payload = data;
    }

    /**
     * Loads the sign PDF waiver data by making a POST request to the API
     * @returns Observable<SignHTMLWaiverResponse>
     */
    load(): Observable<SignHTMLWaiverResponse> {
        const url = this.getApiUrl()
            .replace('{CustomerId}', this._queryParams.CustomerId)
            .replace('{WaiverSetId}', this._queryParams.WaiverSetId);

        // Send payload as a single object
        return this._http.post<SignHTMLWaiverResponse>(url, this._payload);
    }
}

/**
 * @fileoverview Data layer service for handling sign PDF waiver API calls
 * <AUTHOR>
 * @version 1.0.0
 */
import { Injectable } from '@angular/core';
import { ApiServiceBase } from 'lib-app-core';
import { Observable } from 'rxjs';
import {
    SignPDFWaiverPayload,
    SignPDFWaiverResponse,
} from '../../interface/waiver.interface';

@Injectable()
export class SignPdfWaiverDL extends ApiServiceBase {
    private _apiParams!: SignPDFWaiverPayload;

    constructor() {
        super('sign_pdf_waiver', 'POST_PDF_WAIVER');
        this.init();
    }

    /**
     * Builds the API parameters for the sign PDF waiver request
     * @param customerId - The customer ID for the API URL
     * @param waiverSetId - The waiver set ID for the API URL
     * @param data - The sign PDF waiver payload data
     */
    buildApiParams(data: SignPDFWaiverPayload) {
        this._apiParams = data;
    }

    /**
     * Loads the sign PDF waiver data by making a POST request to the API
     * @returns Observable<SignPDFWaiverResponse>
     */
    load(): Observable<SignPDFWaiverResponse> {
        const url = this.getApiUrl();
        // Send payload as an array since the API expects this format
        return this._http.post<SignPDFWaiverResponse>(url, [this._apiParams]);
    }
}

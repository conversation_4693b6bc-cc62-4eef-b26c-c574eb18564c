/**
 * @fileoverview Data layer service for handling get signed waiver content API calls
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-15
 */
import { Injectable } from '@angular/core';
import { ApiServiceBase } from 'lib-app-core';
import { Observable } from 'rxjs';
import { CustomerAPIParams } from '../../interface/customer.interface';
import { CustomerSignedWaiverContentResponseDTO } from '../../models/customer-signed-waiver-content-response-dto.model';

@Injectable()
export class GetSignedWaiverContentDL extends ApiServiceBase {
    private _apiParams!: CustomerAPIParams;

    constructor() {
        super(
            'get_signed_waiver_file_content',
            'GET_SIGNED_WAIVER_FILE_CONTENT'
        );
        this.init();
    }

    /**
     * Builds API parameters for the get signed waiver content request
     * @param data - Customer API parameters
     */
    buildApiParams(data: CustomerAPIParams): void {
        this._apiParams = data;
    }

    /**
     * Loads the signed waiver file content by making a GET request to the API
     * @returns Observable<CustomerSignedWaiverContentResponseDTO> - The signed waiver file content
     */
    load(): Observable<CustomerSignedWaiverContentResponseDTO> {
        const { customerId } = this._apiParams;
        const url = this.getApiUrl().replace('{CustomerId}', customerId);

        return this._http.get<CustomerSignedWaiverContentResponseDTO>(url);
    }
}

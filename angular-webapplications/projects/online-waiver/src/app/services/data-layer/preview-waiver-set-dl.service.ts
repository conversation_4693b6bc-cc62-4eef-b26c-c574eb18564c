/**
 * @fileoverview Data layer service for handling waiver set API calls
 * <AUTHOR>
 * @version 1.0.0
 */
import { Injectable } from '@angular/core';
import { ApiServiceBase, appCoreConstant } from 'lib-app-core';
import { Observable } from 'rxjs';
import { GetAllWaiverSetParams } from '../../interface/reservation.interface';
import { WaiverSetResponse } from '../../interface/waiver.interface';

@Injectable({ providedIn: 'root' })
export class PreviewWaiverSetDL extends ApiServiceBase {
    private _apiParams!: GetAllWaiverSetParams;

    constructor() {
        super(
            appCoreConstant.TRANSFER_STATE_KEYS.PREVIEW_WAIVER_DATA,
            'PREVIEW_WAIVER_JSON'
        );
        this.init();
    }

    buildApiParams(siteId: string, languageId: string) {
        this._apiParams = {
            siteId,
            languageId,
        };
    }

    //Builds the API parameters for the preview waiver set request
    //This method is called by the business layer to set the parameters before making the API call
    load(): Observable<WaiverSetResponse> {
        const { siteId, languageId } = this._apiParams;
        const url = this.getJsonApiUrl()
            .replace('{siteId}', siteId)
            .replace('{languageId}', languageId);
        return this._http.get<WaiverSetResponse>(url);
    }
}

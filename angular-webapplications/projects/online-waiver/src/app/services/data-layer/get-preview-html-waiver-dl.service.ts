/**
 * @fileoverview Data layer service for handling get html waiver API calls
 * <AUTHOR>
 * @version 1.0.0
 */
import { Injectable } from '@angular/core';
import { ApiServiceBase, appCoreConstant } from 'lib-app-core';
import { Observable } from 'rxjs';
import { GetPreviewHtmlWaiverQueryParams } from '../../interface/waiver.interface';
import { GetHtmlWaiverModel } from '../../models/waiver-set-container.model';

@Injectable({ providedIn: 'root' })
export class GetPreviewHtmlWaiverDL extends ApiServiceBase {
    private _queryParams!: GetPreviewHtmlWaiverQueryParams;

    constructor() {
        super(
            appCoreConstant.TRANSFER_STATE_KEYS.PREVIEW_HTML_WAIVER_DATA,
            'PREVIEW_HTML_WAIVER_JSON'
        );
        this.init();
    }

    /**
     * Builds the query parameters for the API request
     * @param data - Query parameters for the request
     */
    buildQueryParams(data: GetPreviewHtmlWaiverQueryParams) {
        this._queryParams = data;
    }

    /**
     * Loads data by making API call with both query parameters and payload
     * @returns Observable with the API response
     */
    load(): Observable<GetHtmlWaiverModel> {
        // Build URL with query parameters
        let url = this.getJsonApiUrl();
        const { siteId, languageId, waiverSetId } = this._queryParams;
        url = url
            .replace('{siteId}', siteId)
            .replace('{languageId}', languageId)
            .replace('{waiverSetId}', waiverSetId);

        return this._http.get<GetHtmlWaiverModel>(url);
    }
}

/**
 * @fileoverview This is the main landing component (home page) for the application.
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-09-18
 */

import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { LandingPageTheme } from '@theme/waiver/waiver.theme';
import {
    PageInterface,
    PARENT_FUNCTION_TOKEN
} from 'lib-app-core';
import { SiteSelectionComponent } from "../../components/site-selection/site-selection.component";
import { SiteBaseComponent } from '../../core/sitebase.component';

@Component({
    standalone: true,
    selector: 'app-landing',
    imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    SiteSelectionComponent
],
    templateUrl: './landing.page.component.html',
    styleUrl: './landing.page.component.scss',
    providers: [
        {
            provide: PARENT_FUNCTION_TOKEN,
            useFactory: (parent: LandingPageComponent) => {
                return parent.getPageTheme
                    ? () => parent.getPageTheme()
                    : () => {};
            },
            deps: [LandingPageComponent], // Ensure that the parent component is resolved
        },
    ],
})
export class LandingPageComponent
    extends SiteBaseComponent
    implements PageInterface
{

    protected getRelevantTheme() {
        return LandingPageTheme;
    }

    getPageTheme() {
        return this.getRelevantTheme();
    }

}

/**
 * @fileoverview MyRelationsComponent is a page component that manages customer relations and participants
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-12
 */
/**
 * @component MyRelationsComponent
 * @description
 * Page component for managing customer relations, displaying primary and minor participants,
 * and providing functionality to add new minor participants. This component handles the
 * display and management of customer relationships.
 *
 * @usage
 * This component is used as a page within the my-account section to display and manage
 * customer relations and participants
 *
 * @inputs
 * - No inputs defined in this component
 *
 * @outputs
 * - No outputs defined in this component
 *
 * @dependencies
 * - CustomerRelationBL: Business logic service for customer relations
 * - RelatedCustomerDL: Data layer service for related customers
 * - PrimaryCustomerDL: Data layer service for primary customers
 * - ModalComponent: UI component for modal dialogs
 * - AddMinorFormModalComponent: Component for adding minor participants
 * - RelationsItemComponent: Component for displaying individual relations
 *
 * @methods
 * - No methods defined in this component
 */

import { AsyncPipe, CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { PageFooterComponent } from 'lib-ui-kit';
import { CustomerRelationBL } from '../../../services/business-layer/customer-relation-bl.service';
import { RelatedCustomerDL } from '../../../services/data-layer/related-customer-dl.service';
import { PrimaryCustomerDL } from '../../../services/data-layer/primary-customer-dl.service';
import { IParticipant } from '../../../interface/customer.interface';
import { RelationsItemComponent } from '../../../components/relations-item/relations-item.component';
import { AddMinorBtnComponent } from '../../../components/add-minor-btn/add-minor-btn.component';
import { switchMap } from 'rxjs';
import { WaiverRoutingServiceBL } from '../../../services/business-layer/waiver-routing-bl.service';
import { LibUserDetailDL } from 'lib/lib-auth/src/lib/data-layer/lib-auth-user-details-dl.service';
/**
 * Page component for managing customer relations and participants
 */
@Component({
    selector: 'app-my-relations',
    imports: [
        CommonModule,
        AsyncPipe,
        PageFooterComponent,
        RelationsItemComponent,
        AddMinorBtnComponent,
        RelationsItemComponent,
    ],
    templateUrl: './my-relations.component.html',
    styleUrl: './my-relations.component.scss',
    providers: [
        CustomerRelationBL,
        RelatedCustomerDL,
        PrimaryCustomerDL,
        WaiverRoutingServiceBL,
        LibUserDetailDL,
    ],
})
export class MyRelationsComponent {
    private _customerRelationBL = inject(CustomerRelationBL);

    primaryParticipant$ = this._customerRelationBL._primaryTrigger.pipe(
        switchMap(() => this._customerRelationBL.getPrimaryCustomerData())
    );

    minorParticipants$ = this._customerRelationBL._minorsTrigger.pipe(
        switchMap(() => this._customerRelationBL.getRelatedCustomerData())
    );

    onAddMinorSuccess(event: boolean): void {
        if (event) {
            this._customerRelationBL.refetchRelatedCustomerData();
        }
    }
}

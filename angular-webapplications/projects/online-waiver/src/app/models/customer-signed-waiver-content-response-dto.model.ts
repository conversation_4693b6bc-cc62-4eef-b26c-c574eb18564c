/**
 * @fileoverview CustomerSignedWaiverContentResponseDTO is a data transfer object for customer signed waiver content response
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-15
 */

import { BaseDTO } from 'lib-app-core';
import { CustomerDTO } from './customer-data-dto.model';

/**
 * @class CustomerSignedWaiverContentResponseDTO
 * @description
 * Data transfer object for the complete customer signed waiver content response.
 * This includes all customer data, signed waivers, and related information.
 */
export class CustomerSignedWaiverContentResponseDTO extends BaseDTO<CustomerSignedWaiverContentResponseDTO> {
    data!: CustomerDTO[];
    customersImage!: string;
    customersIdImage!: string;
    totalPages!: number;
}

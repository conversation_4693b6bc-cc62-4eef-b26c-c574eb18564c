<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center backdrop-blur-sm overflow-hidden"
    (click)="onOverlayClick($event)">
    <div
        class="bg-white rounded-4xl p-8 max-w-md w-11/12 relative shadow-2xl animate-in slide-in-from-top-2 duration-300">
        <!-- Close button -->
        <button
            class="absolute top-4 right-4 border-none rounded-full w-8 h-8 flex items-center justify-center cursor-pointer text-white transition-colors duration-200"
            (click)="onClose()">
            <img src="assets/icons/close-red.svg" alt="Close" class="w-6 h-6" />
        </button>

        <!-- Success icon -->
        <img src="assets/icons/sign-success.svg" alt="Success" class="w-20 h-20 mx-auto mb-4">
        <!-- Title -->
        <h2 class="text-green-600 text-xl font-semibold text-center mb-4 leading-6">
            Waiver signed successfully
        </h2>

        <!-- Description -->
        <p class="text-gray-500 text-sm text-center md:mb-8 mb-5 leading-5">
            You can now continue checking in the participants who signed waivers.
        </p>

        <!-- HTML Waiver Layout - Single Preview/Download -->
        @if (isHtmlWaiver && firstWaiver) {
        <div class="flex gap-6 justify-center">
            <button
                class="flex items-center gap-2 text-indigo-500 hover:text-indigo-600 underline transition-all duration-200 transform hover:scale-105 active:scale-95"
                (click)="onDownload(firstWaiver)">
                <img src="assets/icons/download-blue.svg" alt="Download" />
                Download
            </button>

            <button
                class="flex items-center gap-2 text-indigo-500 hover:text-indigo-600 underline transition-all duration-200 transform hover:scale-105 active:scale-95"
                (click)="onPreview(firstWaiver)">
                <img src="assets/icons/preview-magnifier.svg" alt="Preview" />
                Preview
            </button>
        </div>
        }

        <!-- PDF Waiver Layout - Multiple Participants -->
        @if (isPdfWaiver && signedWaivers.length > 0) {
        <div class="space-y-4">
            @for (waiver of signedWaivers; track waiver.CustomerSignedWaiverId) {
            <div class="bg-surface-lightest rounded-lg px-5 py-2">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                    <div class="flex-1">
                        <h2 class="font-medium text-primary text-sm">
                            {{ waiver.SignedForName || 'Participant' }}
                        </h2>
                    </div>
                    <div class="flex gap-6">
                        <button
                            class="flex items-center gap-1 text-indigo-500 hover:text-indigo-600 underline text-sm transition-all duration-200 transform hover:scale-105 active:scale-95"
                            (click)="onDownload(waiver)">
                            <img src="assets/icons/download-blue.svg" alt="Download" class="w-4 h-4" />
                            Download
                        </button>
                        <button
                            class="flex items-center gap-1 text-indigo-500 hover:text-indigo-600 underline text-sm transition-all duration-200 transform hover:scale-105 active:scale-95"
                            (click)="onPreview(waiver)">
                            <img src="assets/icons/preview-magnifier.svg" alt="Preview" class="w-4 h-4" />
                            Preview
                        </button>
                    </div>
                </div>
            </div>
            }
        </div>
        }
    </div>
</div>
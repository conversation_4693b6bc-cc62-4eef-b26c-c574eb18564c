/**
 * @fileoverview Success popup component for waiver signing
 * <AUTHOR>
 * @version 1.0.0
 */
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from 'lib-ui-kit';
import { CustomerSignedWaiverDTO } from '../../models/customer-signed-waiver-dto.model';

export interface WaiverSuccessData {
    waiverName?: string;
    signedDate?: string;
    downloadUrl?: string;
    previewUrl?: string;
}

export interface WaiverSuccessPopupData {
    waiverType: 'html' | 'pdf';
    signedWaivers: CustomerSignedWaiverDTO[];
    waiverSetId?: number;
    waiverCode?: string;
    isHtmlWaiverSigning?: boolean;
}

@Component({
    selector: 'app-waiver-success-popup',
    standalone: true,
    imports: [CommonModule],
    templateUrl: './waiver-success-popup.component.html',
})
export class WaiverSuccessPopupComponent {
    @Input() data?: WaiverSuccessPopupData;
    @Input() isHtmlWaiverSigning?: boolean;
    @Output() close = new EventEmitter<void>();
    @Output() download = new EventEmitter<{
        waiver: CustomerSignedWaiverDTO;
        type: 'html' | 'pdf';
    }>();
    @Output() preview = new EventEmitter<{
        waiver: CustomerSignedWaiverDTO;
        type: 'html' | 'pdf';
    }>();

    onClose(): void {
        this.close.emit();
    }

    onDownload(waiver: CustomerSignedWaiverDTO): void {
        const isHtml =
            this.isHtmlWaiverSigning || this.data?.isHtmlWaiverSigning;
        this.download.emit({ waiver, type: isHtml ? 'html' : 'pdf' });
    }

    onPreview(waiver: CustomerSignedWaiverDTO): void {
        const isHtml =
            this.isHtmlWaiverSigning || this.data?.isHtmlWaiverSigning;
        this.preview.emit({ waiver, type: isHtml ? 'html' : 'pdf' });
    }

    onOverlayClick(event: Event): void {
        if (event.target === event.currentTarget) {
            this.onClose();
        }
    }

    get isHtmlWaiver(): boolean {
        return (
            this.isHtmlWaiverSigning || this.data?.isHtmlWaiverSigning || false
        );
    }

    get isPdfWaiver(): boolean {
        return !this.isHtmlWaiver;
    }

    get signedWaivers(): CustomerSignedWaiverDTO[] {
        return this.data?.signedWaivers || [];
    }

    get firstWaiver(): CustomerSignedWaiverDTO | null {
        return this.signedWaivers.length > 0 ? this.signedWaivers[0] : null;
    }
}

<!-- Participants Section -->
<section class="bg-surface-white rounded-4xl p-5 grid gap-5 w-[272px] md:w-[340px]">
    <div class="flex flex-col gap-2">
        <h1 i18n="select-participants.select-participants" class="text-sm font-medium">Select participants</h1>
        <!-- Primary Account Holder -->
        @if (customerRelationBL.primaryParticipant()?.loading || customerRelationBL.relatedParticipants()?.loading) {
        <ng-container *ngTemplateOutlet="participantSectionSkeleton"></ng-container>
        }
        @else if (customerRelationBL.primaryParticipant()?.error || customerRelationBL.relatedParticipants()?.error) {
        <section class="p-5 bg-red-50 rounded-3xl flex flex-col gap-2">
            <p i18n="select-participants.error-message" class="text-red-600 text-sm">
                {{ customerRelationBL.primaryParticipant()?.error || customerRelationBL.relatedParticipants()?.error }}
            </p>
        </section>
        }
        @else {
        <app-participant-section [participants]="participantsToDisplay()"
            (participantChange)="onParticipantChange($event)"
            [selectedParticipants]="customerRelationBL.selectedParticipants()" class="max-h-[300px] overflow-y-auto" />
        }
    </div>

    @let primaryParticipant = customerRelationBL.primaryParticipant()?.data;
    @if (primaryParticipant) {
    <div class="flex gap-1 items-center">
        <p i18n="select-participants.include-someone-new" class="text-sm">Include someone new?</p>
        <app-add-minor-btn [label]="'Add them'" [variant]="'link'" class="text-sm"
            [primaryAccountData]="primaryParticipant" (addMinorSuccess)="onAddMinorSuccess($event)" />
    </div>
    }

    <button class="bg-primary text-white px-4 py-2 rounded-4xl max-w-[300px]" (click)="close.emit()">
        <span i18n="select-participants.done">Done</span>
    </button>
</section>

<ng-template #participantSectionSkeleton>
    <section class="p-5 bg-surface-lightest rounded-3xl flex flex-col gap-2">
        <div class="flex gap-2 items-center">
            <lib-skeleton-loader skeletonClass="bg-gray-200 animate-pulse h-4 w-4 rounded-md" />
            <lib-skeleton-loader [count]="2" wrapperClass="flex flex-col gap-1"
                skeletonClass="bg-gray-200 animate-pulse w-[120px] h-4 rounded-md" />
        </div>
    </section>
</ng-template>
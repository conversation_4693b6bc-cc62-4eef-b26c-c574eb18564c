/**
 * @fileoverview SelectParticipantsComponent is a component that displays a select participants component
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-30
 */
/**
 * @component SelectParticipantsComponent
 * @description
 * Displays Primary and related customer details such as firstName, lastName, DOB
 * Allows Adding the new minor and select the participants for signing
 * Also updates the component based on waiver signed and minors added
 *
 * @usage
 * User will be routed to this page with CustomerId and SelectedWaiverSetId as a route params
 *
 * @inputs
 * - CustomerId  : Logged in primary customer Id
 * - SelectedWaiverSetId : The waiver set selected in landing page multi waiver scenario , if only one waiver is configured by default it is considered that ID
 *
 * @outputs
 * - Redirects to sign waiver screen with selected list of customerId's
 *
 * @dependencies
 * - SignWaiverService: Handles API communication related to customer data.
 *
 * @methods
 * - initiateAddParticipants(): Communicates with api service and processes the user data for display.
 * - addMinor(): opens the add minor form to enter the minor details.
 * - saveMinor(): saves the values enetered in add minor form.
 * - signWaiver(): Redirects to sign waiver component with selected list of customerIds
 *
 *  @Tobe_saved
 *  waiverResp , signed waivers resp
 */

import { CommonModule } from '@angular/common';
import { Component, computed, effect, inject, output } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SkeletonLoaderComponent } from 'lib-ui-kit';
import { IParticipant } from '../../interface/customer.interface';
import { CustomerRelationBL } from '../../services/business-layer/customer-relation-bl.service';
import { AddMinorBtnComponent } from '../add-minor-btn/add-minor-btn.component';
import { ParticipantSectionComponent } from '../participant-section/participant-section.component';
import { WaiverRoutingServiceBL } from '../../services/business-layer/waiver-routing-bl.service';

@Component({
    selector: 'app-select-participants',
    imports: [
        ReactiveFormsModule,
        FormsModule,
        ParticipantSectionComponent,
        CommonModule,
        AddMinorBtnComponent,
        SkeletonLoaderComponent,
    ],
    providers: [WaiverRoutingServiceBL],
    templateUrl: './select-participants.component.html',
    styleUrl: './select-participants.component.scss',
})
export class SelectParticipantsComponent {
    readonly customerRelationBL = inject(CustomerRelationBL);
    close = output<void>();
    participantsToDisplay = computed<IParticipant[]>(() => {
        const primaryParticipant =
            this.customerRelationBL.primaryParticipant()?.data;
        const relatedParticipants =
            this.customerRelationBL.relatedParticipants()?.data;

        if (!primaryParticipant) return [];
        if (!relatedParticipants) return [primaryParticipant];

        return [primaryParticipant, ...relatedParticipants];
    });

    constructor() {
        effect(() => {
            const primaryParticipant =
                this.customerRelationBL.primaryParticipant()?.data;
            const relatedParticipants =
                this.customerRelationBL.relatedParticipants()?.data;
            if (primaryParticipant && relatedParticipants) {
                this.customerRelationBL.selectedParticipants.update(() => [
                    primaryParticipant,
                    ...relatedParticipants,
                ]);
            }
        });
    }

    /**
     * Handles participant checkbox changes from the new components
     */
    onParticipantChange(event: {
        participant: IParticipant;
        checked: boolean;
    }): void {
        const participant = event.participant as IParticipant;
        if (event.checked) {
            this.customerRelationBL.selectedParticipants.update((prev) => [
                ...prev.filter((p) => p.id !== participant.id),
                participant,
            ]);
        } else {
            this.customerRelationBL.selectedParticipants.update((prev) =>
                prev.filter((p) => p.id !== participant.id)
            );
        }
    }

    onAddMinorSuccess(event: boolean): void {
        if (event) {
            this.customerRelationBL.refetchRelatedCustomerData();
        }
    }
}

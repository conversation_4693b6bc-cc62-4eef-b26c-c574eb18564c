<div
    class="flex flex-col gap-5 lg:gap-6 bg-surface-lighter md:bg-surface-white px-1 py-4 rounded-4xl md:shadow-lg md:min-h-[calc(100vh-168px)]">
    <!-- Page Header -->
    <div class="px-4">
        <h1 class="text-xl md:text-lg font-semibold text-gray-900 mb-2" i18n="list-waivers.title">Waivers</h1>
        <p class="text-sm text-gray-600" i18n="list-waivers.description">Choose the waiver you'd like to sign</p>
    </div>

    <!-- Waiver List Content -->
    <div class="px-4 pb-4">
        <!-- Loading State -->
        @if (waiverSet$ | async; as waiverSets) {
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4">
            @for (waiverSet of waiverSets; track waiverSet.WaiverSetId) {
            <div class="bg-gray-50 rounded-4xl p-4 cursor-pointer shadow-md hover:bg-gray-100 hover:shadow-md relative transition-all duration-200 ease-in-out active:scale-95"
                (click)="selectWaiver(waiverSet)">

                <div class="flex flex-col gap-1.5 md:gap-2">
                    <h4 class="text-sm font-semibold text-gray-800 m-0 leading-tight pr-16">
                        {{ waiverSet.Name }}
                    </h4>
                    <p class="text-xs text-gray-500 m-0 leading-relaxed">
                        <span i18n="list-multiple-waiver.consent-form-description">Consent form required for minors
                            participating in the</span> {{ waiverSet.Name }}.
                    </p>
                </div>
            </div>
            }
        </div>
        } @else {
        <lib-skeleton-loader [count]="3" wrapperClass="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4"
            skeletonClass="h-16 bg-gray-200 rounded-xl md:rounded-2xl animate-pulse">
        </lib-skeleton-loader>
        }
    </div>
</div>
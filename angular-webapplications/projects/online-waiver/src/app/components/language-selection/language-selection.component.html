<!-- Language Selector -->
<app-dropdown-menu [items]="languageMenuItems()" [alignRight]="true" [defaultSelection]="defaultSelection()">
    <button triggerButton class="flex items-center justify-center gap-1 px-2 py-1 rounded text-xs xl:text-sm">
        <img src="/assets/icons/language.svg" class="w-5 h-5 xl:w-6 xl:h-6" alt="language" />
        @if (selectedLanguage()) {
        <span class="min-w-12">{{ selectedLanguage() }}</span>
        }
        @else {
        <!-- Loading skeleton -->
        <div class="h-3.5 w-10 bg-gray-200 rounded animate-pulse"></div>
        }
        <img src="/assets/icons/chevron-down.svg" alt="Dropdown" class="w-4 h-4 transition-transform" />
    </button>
</app-dropdown-menu>
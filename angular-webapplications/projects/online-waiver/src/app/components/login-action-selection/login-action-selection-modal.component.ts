import { Component, EventEmitter, Output } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'app-login-action-selection',
    standalone: true,
    imports: [CommonModule],
    templateUrl: './login-action-selection.component.html',
})
export class LoginActionSelectionComponent {
    @Output() signWaiverSelected = new EventEmitter<void>();
    @Output() checkInSelected = new EventEmitter<void>();

    onSignWaiverSelect(): void {
        this.signWaiverSelected.emit();
    }

    onCheckInSelect(): void {
        this.checkInSelected.emit();
    }
}

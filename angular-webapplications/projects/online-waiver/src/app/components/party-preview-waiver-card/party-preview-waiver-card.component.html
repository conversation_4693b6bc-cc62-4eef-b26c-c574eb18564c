<!-- Party Preview Waiver Card -->
<div class="bg-white rounded-4xl shadow-lg overflow-hidden max-w-7xl mx-auto p-2">
    <!-- Purple Header Section -->
    <div class="px-6 py-8 text-center rounded-4xl"
        style="background-image: url('assets/icons/party-card-bg.svg'); background-size: cover; background-position: center; background-repeat: no-repeat;">
        <!-- Header Content -->
        <div class="rounded-3xl relative z-10">
            <div class="text-white text-xs font-medium mb-1" i18n="party-preview-waiver-card.welcome-text">Welcome to
            </div>
            <div class="text-white text-xl md:text-3xl font-semibold mb-2">{{ (_waiverDetailsBL.waiverInfo$
                | async)?.productName }}
            </div>
            <div class="text-white text-xs font-medium" i18n="party-preview-waiver-card.get-started-text">Get started by
                signing waivers</div>
        </div>
    </div>

    <!-- White Body Section -->
    <div class="px-6 py-6 bg-white">
        <!-- Event Details and Preview Row -->
        @if (transactionTime$ | async; as transactionTime) {
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <!-- Date and Time Group -->
            <div class="flex items-center gap-10">
                <div class="flex items-center gap-2">
                    <img src="assets/icons/red-calender.svg" alt="Calendar" class="w-5 h-5" />
                    <span class="text-black-900 text-sm font-medium">{{ transactionTime.FromDate |
                        date:'dd MMM, EEE' }}</span>
                </div>
                <div class="flex items-center gap-2">
                    <img src="assets/icons/clock.svg" alt="Clock" class="w-5 h-5" />
                    <span class="text-black-900 text-sm font-medium">{{ transactionTime.FromDate |
                        date:'hh:mm a' }}</span>
                </div>
            </div>

            <!-- Preview Waiver Button -->
            @if (showPreviewButton()) {
            <div class="flex items-center gap-2">
                <img src="assets/icons/preview-green.svg" alt="Preview" class="w-5 h-5" />
                <button (click)="previewWaiverForSinglePartyCurrent()"
                    class="text-blue-600 underline text-sm font-medium hover:text-blue-800 transition-colors">
                    <span i18n="party-preview-waiver-card.preview-waiver-text">Preview waiver</span>
                </button>
            </div>
            }
        </div>
        }
    </div>
</div>

<!-- Preview Waiver Modal -->
<ng-template #showPreviewWaiverContent>
    <app-preview-waiver [waiverData]="selectedWaiverData()" />
</ng-template>

<lib-modal [isOpen]="showPreviewWaiver()" [modalContent]="showPreviewWaiverContent" (closeModal)="closePreviewWaiver()"
    dialogueHeader="Preview waiver" i18n-dialogueHeader="party-preview-waiver-card.preview-waiver-header">
</lib-modal>
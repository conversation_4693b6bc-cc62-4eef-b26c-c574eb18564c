/**
 * @fileoverview Party Preview Waiver Card Component
 * @description This component handles the display and interaction for single party waiver previews.
 *              It provides a specialized UI for individual party bookings with transaction time
 *              display, waiver preview functionality, and modal interactions. The component uses
 *              Angular signals for reactive state management and provides a streamlined user
 *              experience for single party waiver operations.
 * <AUTHOR> G
 * @version 1.0.0
 * @created 2025-08-21
 */

import { Component, inject, signal, effect, input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { map, switchMap } from 'rxjs';
import {
    WaiverPreviewData,
    WaiverSetContainerDTO,
} from '../../interface/waiver.interface';
import { PreviewWaiverComponent } from '../preview-waiver/preview-waiver.component';
import { ModalComponent } from 'lib-ui-kit';
import { WaiverDetailsServiceBL } from '../../services/business-layer/waiver-details-bl.service';
import { WaiverCardStateService } from '../../services/business-layer/waiver-card-state-bl.service';

/**
 * PartyPreviewWaiverCardComponent - Single Party Waiver Preview Handler
 *
 * This component is specifically designed for single party waiver preview scenarios.
 *
 */
@Component({
    selector: 'app-party-preview-waiver-card',
    standalone: true,
    imports: [CommonModule, PreviewWaiverComponent, ModalComponent],
    templateUrl: './party-preview-waiver-card.component.html',
})
export class PartyPreviewWaiverCardComponent {
    /**
     * Waiver Details Business Logic Service - Core Business Operations
     *
     * This service handles all business logic related to waiver operations including:
     * - Waiver information retrieval and management
     * - Transaction time fetching
     * - Waiver preview data processing
     * - Current waiver info state management
     */
    readonly _waiverDetailsBL = inject(WaiverDetailsServiceBL);

    /**
     * Preview Waiver Modal State - Modal Visibility Control
     *
     * Controls the visibility of the waiver preview modal.
     * Uses Angular signal for reactive updates and automatic change detection.
     */
    readonly showPreviewWaiver = signal<boolean>(false);

    /**
     * Selected Waiver Data - Preview Content Storage
     *
     * Stores the currently selected waiver data for preview display.
     * Contains the processed waiver content and metadata needed for the preview modal.
     */
    readonly selectedWaiverData = signal<WaiverPreviewData | null>(null);

    /**
     * Waiver Sets Input Property - External Data Source
     *
     * Receives waiver sets from the parent component as an input property.
     * This allows the component to be reusable and receive data from different sources.
     * The component automatically reacts to changes in this input through the effect.
     */
    readonly waiverSets = input<WaiverSetContainerDTO[]>([]);

    /**
     * Show Preview Button - Visibility Control
     *
     * Controls whether the preview waiver button should be displayed.
     * When true, the preview button is shown; when false, it's hidden.
     * Defaults to true to maintain backward compatibility.
     */
    readonly showPreviewButton = input<boolean>(true);

    /**
     * Selected Waiver Set ID - Current Selection State
     *
     * Tracks the currently selected waiver set ID for preview operations.
     * This signal is updated when a user selects a waiver for preview and
     * triggers the effect to load the corresponding preview data.
     */
    readonly selectedWaiverSetId = signal<number | null>(null);

    constructor() {
        /**
         * Effect for Waiver Preview Data Loading
         *
         * This effect automatically triggers when either selectedWaiverSetId or waiverSets change.
         * It ensures that when a waiver set is selected, the corresponding preview data is loaded
         * and the modal is prepared for display. This reactive approach eliminates the need for
         * manual subscription management and ensures data consistency.
         */
        effect(() => {
            const waiverSetId = this.selectedWaiverSetId();
            const waiverSets = this.waiverSets();

            if (waiverSetId && waiverSets.length > 0) {
                this.loadWaiverPreviewData(waiverSetId, waiverSets);
            }
        });
    }

    /**
     * Transaction Time Observable - Event Timing Information
     *
     * This observable fetches and provides transaction time information for the current waiver.
     * It uses RxJS operators to:
     * - switchMap: Handle sequential API calls based on waiver info changes
     * - map: Transform the response to extract the first transaction time record
     *
     * The observable automatically handles cases where no transaction ID is available
     * by returning an empty array, preventing errors in the template.
     */
    transactionTime$ = this._waiverDetailsBL.waiverInfo$.pipe(
        switchMap((waiverInfo) => {
            if (!waiverInfo?.transactionId) {
                return [];
            }
            return this._waiverDetailsBL.getTransactionTime(
                waiverInfo?.transactionId
            );
        }),
        map((response) => response?.data?.[0] || null)
    );

    /**
     * Close Preview Waiver - Modal State Reset
     *
     * Resets all preview-related state when the modal is closed.
     * This includes:
     * - Hiding the preview modal
     * - Clearing the selected waiver data
     * - Resetting the selected waiver set ID
     *
     * This ensures a clean state for the next preview operation.
     */
    closePreviewWaiver() {
        this.showPreviewWaiver.set(false);
        this.selectedWaiverData.set(null);
        this.selectedWaiverSetId.set(null);
    }

    /**
     * Preview Waiver for Single Party - Waiver Set Selection
     *
     * Sets the selected waiver set ID to trigger the preview loading effect.
     * This method is called when a user clicks the preview button for a specific waiver set.
     *
     * @param waiverSetId - The ID of the waiver set to preview
     */
    previewWaiverForSingleParty(waiverSetId: number) {
        this.selectedWaiverSetId.set(waiverSetId);
    }

    /**
     * Preview Waiver for Single Party Current - Current Waiver Preview
     *
     * Automatically selects the current waiver set for preview based on the
     * waiver information from the business logic service. This method is used
     * when the user wants to preview the waiver associated with their current booking.
     */
    previewWaiverForSinglePartyCurrent() {
        const waiverInfo = this._waiverDetailsBL.getCurrentWaiverInfo();
        const waiverSetId = waiverInfo?.waiverSetId;

        if (waiverSetId) {
            this.previewWaiverForSingleParty(waiverSetId);
        }
    }

    /**
     * Load Waiver Preview Data - Data Processing and Modal Preparation
     *
     * This private method handles the actual loading and processing of waiver preview data.
     * It:
     * 1. Finds the specific waiver set from the provided array
     * 2. Calls the business logic service to get preview data
     * 3. Updates the component state with the preview data
     * 4. Shows the preview modal
     *
     * The method uses a callback approach to avoid subscription management while
     * maintaining reactive state updates.
     *
     * @param waiverSetId - The ID of the waiver set to load
     * @param waiverSets - Array of available waiver sets to search through
     */
    private loadWaiverPreviewData(
        waiverSetId: number,
        waiverSets: WaiverSetContainerDTO[]
    ) {
        // Find the specific waiver set
        const waiverSet = waiverSets.find(
            (waiverSet) => waiverSet.WaiverSetId === waiverSetId
        );

        if (waiverSet) {
            // Use the existing business layer method without subscribe
            this._waiverDetailsBL.getWaiverPreviewDataWithState(
                waiverSet,
                (waiverData) => {
                    this.selectedWaiverData.set(waiverData);
                    this.showPreviewWaiver.set(true);
                }
            );
        }
    }
}

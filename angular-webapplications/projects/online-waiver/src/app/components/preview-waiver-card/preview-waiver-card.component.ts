/**
 * @fileoverview Preview Waiver Card Component
 * @description This component serves as the main container for displaying waiver preview cards.
 *              It acts as a smart router that determines whether to show a single party preview
 *              card or a default preview card based on the waiver state. The component manages
 *              loading states, error handling, and provides a centralized state management
 *              interface for all waiver preview operations.
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-21
 */

import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PartyPreviewWaiverCardComponent } from '../party-preview-waiver-card/party-preview-waiver-card.component';
import { DefaultPreviewWaiverCardComponent } from '../default-preview-waiver-card/default-preview-waiver-card.component';
import { SkeletonLoaderComponent } from 'lib-ui-kit';
import { WaiverCardStateService } from '../../services/business-layer/waiver-card-state-bl.service';
import { TransactionDetailsDL } from 'lib-app-core';
import { WaiverSetDL } from '../../services/data-layer/waiver-set-dl.service';
import { TransactionTimeDL } from 'lib-app-core';
import { SiteViewsServiceBL } from 'lib/lib-app-core/src/lib/services/business-layer/site-views-bl.service';
import { CreateDefaultCustomerDL } from '../../services/data-layer/create-default-customer-dl.service';
import { GetHtmlWaiverDL } from '../../services/data-layer/get-html-waiver-dl.service';

/**
 * PreviewWaiverCardComponent - Main Waiver Preview Container
 *
 * This component is the primary orchestrator for waiver preview functionality in the application.
 */
@Component({
    selector: 'app-preview-waiver-card',
    standalone: true,
    imports: [
        CommonModule,
        PartyPreviewWaiverCardComponent,
        DefaultPreviewWaiverCardComponent,
        SkeletonLoaderComponent,
    ],
    templateUrl: './preview-waiver-card.component.html',
    providers: [
        TransactionDetailsDL,
        WaiverSetDL,
        TransactionTimeDL,
        SiteViewsServiceBL,
        CreateDefaultCustomerDL,
        GetHtmlWaiverDL,
        WaiverCardStateService,
    ],
})
export class PreviewWaiverCardComponent {
    /**
     * Waiver Card State Service - Centralized State Management
     *
     * This service provides reactive state management for all waiver-related operations.
     * It handles:
     * - Loading states for waiver data fetching
     * - Error handling and recovery
     * - Waiver set filtering and processing
     * - Party type detection (single vs multiple parties)
     * - Reactive state updates using RxJS observables
     */
    readonly _waiverStateService = inject(WaiverCardStateService);

    /**
     * Party Card State Observable - Main State Stream
     *
     * This observable provides the complete state of waiver operations including:
     * - loading: Boolean indicating if data is being fetched
     * - error: String containing error message or null
     * - waiverSets: Array of available waiver sets
     * - isPartyCard: Boolean indicating if this is a party card scenario
     *
     * The template uses this observable with the async pipe to automatically
     * handle subscription and unsubscription, ensuring proper memory management.
     */
    readonly partyCardState$ = this._waiverStateService.waiverState$;
}

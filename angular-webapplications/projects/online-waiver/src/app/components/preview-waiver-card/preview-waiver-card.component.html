<!-- Preview Waiver Card Component Template -->
<div class="preview-waiver-card mb-6">
    @if (partyCardState$ | async; as state) {
    @if (state.loading) {
    <!-- Loading State -->
    <section class="p-5 bg-surface-lightest rounded-3xl flex flex-col gap-2">
        <lib-skeleton-loader [count]="2" wrapperClass="flex flex-col gap-2"
            skeletonClass="h-4 bg-gray-200 rounded animate-pulse">
        </lib-skeleton-loader>
    </section>
    } @else if (state.waiverSets) {
    <!-- Success State -->
    @if (state.isPartyCard) {
    <app-party-preview-waiver-card [waiverSets]="state.waiverSets">
    </app-party-preview-waiver-card>
    } @else {
    <app-default-preview-waiver-card></app-default-preview-waiver-card>
    }
    }
    }
</div>
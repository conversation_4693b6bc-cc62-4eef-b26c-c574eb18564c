/**
 * @fileoverview Failure popup component for waiver signing
 * <AUTHOR>
 * @version 1.0.0
 */
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from 'lib-ui-kit';

export interface WaiverFailureData {
    errorMessage?: string;
    errorCode?: string;
    retryAllowed?: boolean;
}

@Component({
    selector: 'app-waiver-failure-popup',
    standalone: true,
    imports: [CommonModule, ButtonComponent],
    templateUrl: './waiver-failure-popup.component.html',
})
export class WaiverFailurePopupComponent {
    @Input() data?: WaiverFailureData;
    @Output() close = new EventEmitter<void>();
    @Output() tryAgain = new EventEmitter<void>();

    onClose(): void {
        this.close.emit();
    }

    onOverlayClick(event: Event): void {
        if (event.target === event.currentTarget) {
            this.onClose();
        }
    }
}

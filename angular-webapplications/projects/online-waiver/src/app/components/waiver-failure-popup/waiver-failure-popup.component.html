<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center backdrop-blur-sm overflow-hidden"
    (click)="onOverlayClick($event)">
    <div
        class="bg-white rounded-4xl p-6 max-w-md w-11/12 relative shadow-2xl animate-in slide-in-from-top-2 duration-300">
        <!-- Close button -->
        <button
            class="absolute top-4 right-4 border-none rounded-full w-8 h-8 flex items-center justify-center cursor-pointer text-white transition-colors duration-200"
            (click)="onClose()">
            <img src="assets/icons/close-red.svg" alt="Close" class="w-6 h-6" />
        </button>

        <!-- Failure icon -->
        <img src="assets/icons/sign-failure.svg" alt="Failure" class="w-20 h-20 mx-auto mb-4">

        <!-- Title -->
        <h2 class="text-red-700 text-xl font-semibold text-center mb-4 leading-6">
            Waiver signing failed
        </h2>

        <!-- Description -->
        <p class="text-gray-500 text-sm text-center mb-6 leading-5">
            If you encounter an issue while signing the waiver, please check the fields for any errors. You can try
            again to complete the process.
        </p>

        <!-- Action button -->
        <div class="flex justify-center mt-auto">
            <lib-button type="primary" size="md" [disabled]="!data?.retryAllowed" (clicked)="onClose()">
                Try again
            </lib-button>
        </div>
    </div>
</div>
<div class="bg-white rounded-4xl shadow-sm border border-gray-100 p-5 md:p-6">
    <!-- Participants Section Header -->
    <div class="flex flex-col gap-2 md:flex-row md:items-center">
        <div class="md:flex-1">
            <h3 i18n="participant-card.participants" class="text-lg font-medium text-gray-900 md:mb-1">
                Participants ({{ participantCount() }})
            </h3>
            @if(loading()) {
            <lib-skeleton-loader
                skeletonClass='h-[20px] w-full max-w-[300px] animate-pulse bg-surface  rounded-md'></lib-skeleton-loader>
            } @else {
            @if(participantNames().length > 0) {
            <p i18n="participant-card.participants-names" class="text-sm text-gray-500 font-medium min-h-[20px]">
                {{ participantNames() }}
            </p>
            } @else {
            <p i18n="participant-card.no-participants" class="text-sm text-gray-500 font-medium min-h-[20px]">
                No participants added yet
            </p>
            }
            }
        </div>

        <!-- Add or Remove Participants Link -->
        <lib-popup #addOrRemoveParticipantsPopup [align]="'end'">
            <button popupTrigger>
                <span i18n="participant-card.add-or-remove-participants"
                    class="text-blue-600 hover:text-blue-700 underline text-sm font-medium">Add or remove
                    participants</span>
            </button>
            <app-select-participants popupContent
                (close)="addOrRemoveParticipantsPopup.close()"></app-select-participants>
        </lib-popup>
    </div>
</div>
import { CommonModule } from '@angular/common';
import { Component, computed, inject, OnInit } from '@angular/core';
import { SelectParticipantsComponent } from '../select-participants/select-participants.component';
import { CustomerRelationBL } from '../../services/business-layer/customer-relation-bl.service';
import { PopupComponent, SkeletonLoaderComponent } from 'lib-ui-kit';

@Component({
    selector: 'app-participant-card',
    standalone: true,
    imports: [
        CommonModule,
        SelectParticipantsComponent,
        PopupComponent,
        SkeletonLoaderComponent,
    ],
    templateUrl: './participant-card.component.html',
})
export class ParticipantCardComponent implements OnInit {
    readonly customerRelationBL = inject(CustomerRelationBL);
    readonly loading = computed(() => {
        return (
            this.customerRelationBL.primaryParticipant()?.loading ||
            this.customerRelationBL.relatedParticipants()?.loading
        );
    });

    readonly participantCount = computed(() => {
        if (this.loading()) {
            return 0;
        }
        return this.customerRelationBL.selectedParticipants().length;
    });

    readonly participantNames = computed(() => {
        return this.customerRelationBL
            .selectedParticipants()
            .map((p) => `${p.firstName} ${p.lastName}`)
            .join(', ');
    });

    ngOnInit() {
        this.customerRelationBL.refetchPrimaryCustomerData();
        this.customerRelationBL.refetchRelatedCustomerData();
    }
}

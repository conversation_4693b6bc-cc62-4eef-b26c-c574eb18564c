<div class="signing-pad-container">
    <!-- Canvas Container -->
    <div class="canvas-wrapper relative">
        <canvas #signatureCanvas
            class="signature-canvas w-[305px] h-[246px] md:w-[432px] md:h-[246px] bg-white border-2 border-dashed border-gray-300 rounded-lg cursor-crosshair">
        </canvas>

        <!-- Clear Text - Overlay on Canvas -->
        <div class="absolute top-1 right-2 z-10 pr-2">
            <span class="text-sm text-black-900 cursor-pointer hover:text-gray-700 transition-colors"
                (click)="clearSignature()">
                Clear
            </span>
        </div>

        <!-- Placeholder Text -->
        @if (!hasSignature()) {
        <div class="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div class="text-center text-gray-400">
                <div class="text-sm font-medium">Draw your signature here</div>
                <div class="text-xs mt-1">Use your mouse or finger to sign</div>
            </div>
        </div>
        }
    </div>

    <!-- Action Buttons -->
    <div class="flex gap-3 mt-4">
        <button type="button"
            class="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-3xl hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            [disabled]="!hasSignature() || isSubmitting()" (click)="submitSignature()">
            @if (isSubmitting()) {
            <div class="flex items-center justify-center">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Submitting...
            </div>
            } @else {
            Submit
            }
        </button>
    </div>
</div>
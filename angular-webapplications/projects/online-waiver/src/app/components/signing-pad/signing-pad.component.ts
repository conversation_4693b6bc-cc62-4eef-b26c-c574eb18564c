import {
    Component,
    output,
    input,
    ElementRef,
    ViewChild,
    AfterViewInit,
    inject,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { SignatureBlService } from '../../services/business-layer/signature-bl.service';

@Component({
    selector: 'app-signing-pad',
    standalone: true,
    imports: [CommonModule],
    templateUrl: './signing-pad.component.html',
    providers: [SignatureBlService],
})
export class SigningPadComponent implements AfterViewInit {
    @ViewChild('signatureCanvas', { static: false })
    signatureCanvas!: ElementRef<HTMLCanvasElement>;
    private _signatureBlService = inject(SignatureBlService);

    // Inputs
    readonly isSubmitting = input<boolean>(false);

    // Outputs
    readonly signatureData = output<string>();

    // Expose service signals for template
    get isDrawing() {
        return this._signatureBlService.isDrawing;
    }

    get hasSignature() {
        return this._signatureBlService.hasSignature;
    }

    ngAfterViewInit(): void {
        this.initializeCanvas();
    }

    private initializeCanvas(): void {
        const canvas = this.signatureCanvas.nativeElement;
        this._signatureBlService.initializeCanvas(canvas);
    }

    clearSignature(): void {
        const canvas = this.signatureCanvas.nativeElement;
        this._signatureBlService.clearSignature(canvas);
    }

    submitSignature(): void {
        if (!this.hasSignature()) {
            return;
        }

        const canvas = this.signatureCanvas.nativeElement;
        const decodedBase64 =
            this._signatureBlService.getSignatureAsDecodedBase64(canvas);
        this.signatureData.emit(decodedBase64);
    }
}

<!-- Default Preview Waiver Card UI -->
<div class="bg-surface-white rounded-4xl shadow-lg p-4 md:p-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-xl font-semibold text-primary" i18n="default-preview-waiver-card.sign-waivers-title">Sign
                waivers</h1>
            <p class="text-sm text-primary mt-1">
                <span i18n="default-preview-waiver-card.sign-waivers-description">Sign waivers to get access to the
                    services.</span>
            </p>
        </div>
        <div class="flex items-center gap-4 mt-4 md:mt-0">
            @if (waiverSet$ | async; as waiverSets) {
            <!-- Show dropdown for multiple waiver sets -->
            @if (waiverSets && waiverSets.length > 1) {
            <div>
                <app-dropdown-menu [alignRight]="dropdownAlignment()" [useCustomTemplate]="true" width="w-80"
                    [customTemplate]="waiverListTemplate"
                    [customHeader]="'Select a waiver from below for details on policies and requirements.'"
                    i18n-customHeader="default-preview-waiver-card.multiple-waivers-header" [size]="dropdownSize()"
                    [templateContext]="{ waiverSets: waiverSets }">

                    <!-- Trigger Button (Content Projection) -->
                    <button triggerButton class="flex items-center gap-1 text-sm text-secondary-blue underline">
                        <img src="assets/icons/preview-green.svg" alt="Preview" />
                        <span class="text-left" i18n="default-preview-waiver-card.preview-waiver-text">Preview
                            waiver</span>
                    </button>
                </app-dropdown-menu>
            </div>
            }

            <!-- Show direct preview button for single waiver set -->
            @if (waiverSets && waiverSets.length === 1) {
            <button class="flex items-center gap-1 text-sm text-secondary-blue underline"
                (click)="selectWaiverFromDropdown(waiverSets[0])">
                <img src="assets/icons/preview-green.svg" alt="Preview" />
                <span class="text-left" i18n="default-preview-waiver-card.preview-waiver-text">Preview waiver</span>
            </button>
            }
            }
        </div>
    </div>
</div>

<!-- Template for Waiver List in Dropdown -->
<ng-template #waiverListTemplate let-waiverSets="waiverSets">
        <div class="flex flex-col gap-3">
            @for (waiverSet of waiverSets; track waiverSet.WaiverSetId) {
            <div class="bg-surface-lightest p-4 cursor-pointer rounded-4xl transition-all duration-100 ease-in-out hover:bg-blue-50 hover:shadow-md active:scale-95"
                (click)="selectWaiverFromDropdown(waiverSet)">
                <div class="flex flex-col gap-1.5">
                    <h4 class="text-sm font-medium text-gray-900 m-0 leading-5">
                        {{ waiverSet.Name }}
                    </h4>
                    <p class="text-xs text-gray-600 m-0 leading-relaxed ">
                        <span i18n="default-preview-waiver-card.consent-form-description">Consent form required for
                            minors participating in the</span> {{ waiverSet.Name }}.
                    </p>
                </div>
            </div>
            }
        </div>
</ng-template>

<!-- Preview Waiver Modal -->
<ng-template #showPreviewWaiverContent>
    <app-preview-waiver [waiverData]="selectedWaiverData()" />
</ng-template>

<lib-modal [isOpen]="showPreviewWaiver()" [modalContent]="showPreviewWaiverContent" (closeModal)="closePreviewWaiver()"
    dialogueHeader="Preview waiver" i18n-dialogueHeader="default-preview-waiver-card.preview-waiver-header">
</lib-modal>
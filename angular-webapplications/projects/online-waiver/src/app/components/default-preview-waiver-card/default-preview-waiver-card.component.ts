/**
 * @fileoverview Default Preview Waiver Card Component
 * @description This component handles the display and interaction for multiple waiver preview scenarios.
 *              It provides a flexible UI that adapts to different numbers of waiver sets, offering
 *              either a dropdown selection for multiple waivers or a direct preview button for single
 *              waivers. The component includes responsive design features and modal management for
 *              waiver preview functionality.
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-21
 */

import {
    Component,
    inject,
    signal,
    HostListener,
    PLATFORM_ID,
} from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import {
    WaiverPreviewData,
    WaiverSetContainerDTO,
} from '../../interface/waiver.interface';
import { PreviewWaiverComponent } from '../preview-waiver/preview-waiver.component';
import { ModalComponent } from 'lib-ui-kit';
import { DropdownMenuComponent } from '../../shared/dropdown-menu/dropdown-menu.component';
import { WaiverDetailsServiceBL } from '../../services/business-layer/waiver-details-bl.service';
import { WaiverCardStateService } from '../../services/business-layer/waiver-card-state-bl.service';

/**
 * DefaultPreviewWaiverCardComponent - Multiple Waiver Preview Handler
 *
 * This component is designed to handle waiver preview scenarios where multiple waiver sets
 * may be available.
 */
@Component({
    selector: 'app-default-preview-waiver-card',
    standalone: true,
    imports: [
        CommonModule,
        PreviewWaiverComponent,
        ModalComponent,
        DropdownMenuComponent,
    ],
    templateUrl: './default-preview-waiver-card.component.html',
})
export class DefaultPreviewWaiverCardComponent {
    /**
     * Waiver Details Business Logic Service - Core Business Operations
     *
     * This service handles all business logic related to waiver operations including:
     * - Waiver information retrieval and management
     * - Waiver preview data processing
     * - Business rule enforcement and data validation
     */
    readonly _waiverDetailsBL = inject(WaiverDetailsServiceBL);

    /**
     * Waiver Card State Service - Centralized State Management
     *
     * Provides reactive state management for all waiver-related operations including:
     * - Loading states for waiver data fetching
     * - Error handling and recovery
     * - Waiver set filtering and processing
     * - Reactive state updates using RxJS observables
     */
    readonly _waiverStateService = inject(WaiverCardStateService);

    /**
     * Preview Waiver Modal State - Modal Visibility Control
     *
     * Controls the visibility of the waiver preview modal.
     * Uses Angular signal for reactive updates and automatic change detection.
     */
    readonly showPreviewWaiver = signal<boolean>(false);

    /**
     * Selected Waiver Data - Preview Content Storage
     *
     * Stores the currently selected waiver data for preview display.
     * Contains the processed waiver content and metadata needed for the preview modal.
     */
    readonly selectedWaiverData = signal<WaiverPreviewData | null>(null);

    /**
     * Platform ID - Server-Side Rendering Support
     *
     * Injected platform ID to detect whether the code is running in a browser
     * or during server-side rendering. This ensures proper behavior in both environments.
     */
    readonly platformId = inject(PLATFORM_ID);

    /**
     * Mobile Breakpoint - Responsive Design Threshold
     *
     * Defines the screen width threshold for mobile devices.
     * Used to determine when to switch between mobile and desktop UI behaviors.
     */
    private readonly MOBILE_BREAKPOINT = 768;

    /**
     * Browser Detection - Platform Environment Check
     *
     * Boolean flag indicating whether the code is running in a browser environment.
     * Used to prevent server-side rendering issues with browser-specific APIs.
     */
    private readonly isBrowser = isPlatformBrowser(this.platformId);

    /**
     * Dropdown Alignment - Responsive UI Positioning
     *
     * Controls the alignment of the dropdown menu based on screen size.
     * Uses Angular signal for reactive updates when screen size changes.
     */
    readonly dropdownAlignment = signal<boolean>(true);

    /**
     * Dropdown Size - Responsive UI Sizing
     *
     * Controls the size of the dropdown menu based on screen size.
     * Switches between 'sm' (small) for mobile and 'lg' (large) for desktop.
     */
    readonly dropdownSize = signal<'sm' | 'md' | 'lg'>('lg');

    constructor() {
        /**
         * Initialize Responsive Settings
         *
         * Sets up initial responsive behavior based on current screen size.
         * This ensures the component starts with the correct UI configuration.
         */
        this.updateResponsiveSettings();
    }

    /**
     * Window Resize Event Handler - Responsive Behavior Management
     *
     * Listens for window resize events and updates responsive settings accordingly.
     * Only executes in browser environment to prevent server-side rendering issues.
     * Uses HostListener decorator for automatic event binding and cleanup.
     */
    @HostListener('window:resize')
    private onResize(): void {
        this.updateResponsiveSettings();
    }

    /**
     * Update Responsive Settings - Dynamic UI Adaptation
     *
     * Updates dropdown alignment and size based on current screen width.
     * This method ensures the UI adapts properly to different screen sizes:
     * - Mobile (< 768px): Right-aligned dropdown with small size
     * - Desktop (≥ 768px): Left-aligned dropdown with large size
     *
     * The method includes safety checks to prevent execution during server-side rendering.
     */
    private updateResponsiveSettings(): void {
        if (!this.isBrowser) return;

        const isMobile = window.innerWidth < this.MOBILE_BREAKPOINT;
        this.dropdownAlignment.set(!isMobile);
        this.dropdownSize.set(isMobile ? 'sm' : 'lg');
    }

    /**
     * Waiver Set Observable - Centralized Data Stream
     *
     * Provides access to the current waiver sets from the centralized state service.
     * This observable automatically updates when waiver data changes and provides
     * the component with the latest waiver information for display and interaction.
     */
    readonly waiverSet$ = this._waiverStateService.waiverSets$;

    /**
     * Close Preview Waiver - Modal State Reset
     *
     * Resets all preview-related state when the modal is closed.
     * This includes:
     * - Hiding the preview modal
     * - Clearing the selected waiver data
     *
     * This ensures a clean state for the next preview operation.
     */
    closePreviewWaiver() {
        this.showPreviewWaiver.set(false);
        this.selectedWaiverData.set(null);
    }

    /**
     * Select Waiver From Dropdown - Waiver Preview Trigger
     *
     * Handles the selection of a waiver from the dropdown menu and triggers
     * the preview functionality. This method:
     * 1. Calls the business logic service to get preview data for the selected waiver
     * 2. Updates the component state with the preview data
     * 3. Shows the preview modal
     *
     * The method uses a callback approach to avoid subscription management while
     * maintaining reactive state updates.
     *
     * @param waiverSet - The selected waiver set to preview
     */
    selectWaiverFromDropdown(waiverSet: WaiverSetContainerDTO) {
        this._waiverDetailsBL.getWaiverPreviewDataWithState(
            waiverSet,
            (waiverData) => {
                this.selectedWaiverData.set(waiverData);
                this.showPreviewWaiver.set(true);
            }
        );
    }
}

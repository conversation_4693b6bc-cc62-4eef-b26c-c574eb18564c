/**
 * @fileoverview SignWaiverComponent handles the detailed view of a specific waiver
 * @description This component displays waiver details, allows users to preview waivers,
 * and manages the waiver signing process. It handles navigation, data loading,
 * and user interactions for the detailed waiver page.
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-10
 */

import {
    Component,
    input,
    inject,
    signal,
    computed,
    OnDestroy,
    effect,
    ViewChild,
    ElementRef,
    AfterViewInit,
    ChangeDetectorRef,
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { CommonModule } from '@angular/common';

import { Router, ActivatedRoute, Params } from '@angular/router';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { PreviewWaiverComponent } from '../preview-waiver/preview-waiver.component';
import { ParticipantCardComponent } from '../participant-card/participant-card.component';
import {
    WaiverPreviewData,
    WaiverSetContainerDTO,
} from '../../interface/waiver.interface';
import { SignPdfWaiverBLService } from '../../services/business-layer/sign-pdf-waiver-bl.service';
import { WaiverDetailsServiceBL } from '../../services/business-layer/waiver-details-bl.service';
import {
    map,
    switchMap,
    tap,
    catchError,
    of,
    startWith,
    Subject,
    EMPTY,
    shareReplay,
    takeUntil,
    distinctUntilChanged,
    firstValueFrom,
} from 'rxjs';
import { ModalComponent, ButtonComponent } from 'lib-ui-kit';
import { BreadCrumbService } from '../../shared/services/bread-crumb.service';
import { SigningPadComponent } from '../signing-pad/signing-pad.component';
import { SignPdfWaiverDL } from '../../services/data-layer/sign-pdf-waiver-dl.service';
import { PrimaryCustomerDL } from '../../services/data-layer/primary-customer-dl.service';
import { RelatedCustomerDL } from '../../services/data-layer/related-customer-dl.service';
import { CustomerRelationBL } from '../../services/business-layer/customer-relation-bl.service';
import { WaiverSigningStateService } from '../../services/business-layer/waiver-signing-state.service';
import {
    WaiverSuccessPopupComponent,
    WaiverSuccessData,
    WaiverSuccessPopupData,
} from '../waiver-success-popup/waiver-success-popup.component';
import {
    WaiverFailurePopupComponent,
    WaiverFailureData,
} from '../waiver-failure-popup/waiver-failure-popup.component';

import { GetSignedWaiverContentBL } from '../../services/business-layer/get-signed-waiver-content-bl.service';
import { GetSignedWaiverContentDL } from '../../services/data-layer/get-signed-waiver-content-dl.service';
import { HtmlWaiverSigningBLService } from '../../services/business-layer/html-waiver-signing-bl.service';
import {
    HtmlWaiverData,
    SignWaiverRouteParams,
} from '../../interface/pdf-waiver-content.interface';
import { SafeHtmlPipe } from '../../shared/pipes/safe-html.pipe';
import { SignHtmlWaiverBLService } from '../../services/business-layer/sign-html-waiver-bl.service';
import { SignHtmlWaiverDL } from '../../services/data-layer/sign-html-waiver-dl.service';
import { CookieService } from 'lib-app-core';
import { WaiverRoutingServiceBL } from '../../services/business-layer/waiver-routing-bl.service';
import { LoginEncryptionService } from 'lib/lib-auth/src/lib/services/login-encryption.service';
import { Base64PdfBLService } from '../../services/buisiness-layer/base64-pdf-bl.service';
import { WaiverPreviewBLService } from '../../services/business-layer/waiver-preview-bl.service';
import { LibUserDetailDL } from 'lib/lib-auth/src/lib/data-layer/lib-auth-user-details-dl.service';
import { CustomerSignedWaiverDTO } from '../../models/customer-signed-waiver-dto.model';

/**
 * Type guard to validate route parameters
 */
function isValidWaiverParams(params: Params): params is SignWaiverRouteParams {
    return (
        typeof params['waiverSetId'] === 'string' &&
        params['waiverSetId'].length > 0
    );
}

/**
 * Component for displaying detailed waiver information and managing waiver interactions
 *
 * This component provides:

 * - Waiver preview functionality (PDF and HTML)
 * - Participant information display
 * - Navigation between waiver sections
 * - Breadcrumb management
 * - Waiver data loading and state management

 * - Modern reactive patterns with signals and RxJS streams
 */
@Component({
    selector: 'app-sign-waiver',
    standalone: true,
    imports: [
        CommonModule,
        PreviewWaiverComponent,
        ParticipantCardComponent,
        ButtonComponent,
        SigningPadComponent,
        ModalComponent,
        WaiverSuccessPopupComponent,
        WaiverFailurePopupComponent,

        SafeHtmlPipe,
    ],
    templateUrl: './sign-waiver.component.html',
    styleUrl: './sign-waiver.component.scss',
    providers: [
        SignPdfWaiverBLService,
        WaiverSigningStateService,
        SignPdfWaiverDL,
        PrimaryCustomerDL,
        RelatedCustomerDL,
        CustomerRelationBL,
        WaiverRoutingServiceBL,
        GetSignedWaiverContentBL,
        GetSignedWaiverContentDL,
        HtmlWaiverSigningBLService,
        SignHtmlWaiverBLService,
        SignHtmlWaiverDL,
        LoginEncryptionService,
        Base64PdfBLService,
        WaiverPreviewBLService,
        LibUserDetailDL,
    ],
})
export class SignWaiverComponent implements OnDestroy, AfterViewInit {
    // ViewChild references for signed waiver PDF rendering
    @ViewChild('signedPdfContainer', { static: false })
    signedPdfContainer!: ElementRef<HTMLDivElement>;

    // Track if view is initialized for signed waiver PDF rendering
    private signedViewInitialized = false;
    // Core component state signals
    readonly waiverData = signal<WaiverPreviewData | null>(null);
    readonly waiverName = signal<string>('');
    readonly currentWaiverSet = signal<WaiverSetContainerDTO | null>(null);
    readonly isWaiverSigned = signal<boolean>(false);
    readonly isLoading = signal<boolean>(true);
    readonly hasError = signal<boolean>(false);
    readonly errorMessage = signal<string>('');
    readonly currentWaiverCode = signal<string>('');

    // Modal and UI state signals
    readonly showSignatureModal = signal<boolean>(false);
    readonly selectedWaiverIndex = signal<number>(-1);
    readonly isSigning = signal<boolean>(false);
    readonly showSuccessPopup = signal<boolean>(false);
    readonly showFailurePopup = signal<boolean>(false);
    readonly successData = signal<WaiverSuccessPopupData | undefined>(
        undefined
    );
    readonly failureData = signal<WaiverFailureData | undefined>(undefined);

    // Signature submission reactive stream
    private readonly signatureSubmission$ = new Subject<string>();
    private readonly destroy$ = new Subject<void>();

    // Dependency injections
    readonly router = inject(Router);
    readonly route = inject(ActivatedRoute);
    private readonly _waiverDetailsBL = inject(WaiverDetailsServiceBL);
    private readonly _signingStateService = inject(WaiverSigningStateService);
    private readonly breadcrumbService = inject(BreadCrumbService);

    readonly _getSignedWaiverContentBL = inject(GetSignedWaiverContentBL);
    private readonly sanitizer = inject(DomSanitizer);
    private readonly _htmlWaiverSigningBL = inject(HtmlWaiverSigningBLService);
    private readonly _cookieService = inject(CookieService);
    private readonly _customerRelationBL = inject(CustomerRelationBL);
    private readonly _waiverPreviewService = inject(WaiverPreviewBLService);
    private readonly _base64PdfService = inject(Base64PdfBLService);

    private readonly _cdr = inject(ChangeDetectorRef);

    // Convert route params to signal with distinctUntilChanged to prevent unnecessary re-executions
    readonly routeParams = toSignal(
        this.route.params.pipe(
            distinctUntilChanged(
                (prev, curr) => prev['waiverSetId'] === curr['waiverSetId']
            )
        ),
        { initialValue: {} }
    );

    // Track if we've already loaded data for current waiverSetId to prevent duplicate API calls
    private lastLoadedWaiverSetId = signal<string | null>(null);

    // Computed properties for PDF URL sanitization
    readonly sanitizedPdfUrl = computed<SafeResourceUrl | null>(() => {
        const rawBlobUrl = this._getSignedWaiverContentBL.pdfBlobUrl();
        if (rawBlobUrl) {
            return this.sanitizer.bypassSecurityTrustResourceUrl(rawBlobUrl);
        }
        return null;
    });

    // Computed properties for waiver type detection
    readonly isPdfWaiver = computed(() => this.waiverData()?.type === 'pdf');
    readonly canSignWaiver = computed(
        () => !this.isWaiverSigned() && !this.isSigning()
    );
    readonly shouldShowCheckIn = computed(() => this.isWaiverSigned());

    // HTML Waiver related computed properties
    readonly isHtmlWaiverSigning = computed(() =>
        this._htmlWaiverSigningBL.isHtmlWaiverSigningInProgress()
    );
    readonly currentHtmlWaiverContent = computed(() =>
        this._htmlWaiverSigningBL.getCurrentHtmlWaiverContent()
    );

    // Computed loading state - no more observable needed
    readonly isLoadingComputed = computed(() => this.isLoading());

    // Signed Waiver PDF rendering signals
    readonly isSignedPdfRendering = signal<boolean>(false);
    readonly signedWaiverPdfData = computed(() => {
        const signedWaiver =
            this._getSignedWaiverContentBL.signedWaiverPreview();
        return signedWaiver?.SignedWaiverFileContentInBase64Format || null;
    });
    readonly pdfPageNumbers = signal<number[]>([]);

    constructor() {
        // Effect to handle route parameter changes and load data
        effect(() => {
            const params = this.routeParams();
            if (params && isValidWaiverParams(params)) {
                const waiverSetId = params.waiverSetId;

                // Only load if we haven't loaded this waiverSetId before
                if (this.lastLoadedWaiverSetId() !== waiverSetId) {
                    this.lastLoadedWaiverSetId.set(waiverSetId);
                    this.loadWaiverData(params);
                }
            }
        });

        // Effect to monitor waiver data changes and start HTML waiver signing
        effect(() => {
            const waiverData = this.waiverData();
            if (waiverData?.type === 'html' && !this.isWaiverSigned()) {
                this.startHtmlWaiverSigning();
            }
        });

        // Subscribe to signature submission results for automatic handling
        this.signatureSubmissionResult$.subscribe();

        // Effect to handle signed waiver PDF rendering when modal opens
        effect(() => {
            const showModal = this._getSignedWaiverContentBL.showModal();
            const pdfData = this.signedWaiverPdfData();

            if (showModal && pdfData && this.signedViewInitialized) {
                // Small delay to ensure modal is rendered before accessing container
                setTimeout(() => {
                    this.renderSignedWaiverPdf();
                }, 100);
            }
        });
    }

    ngAfterViewInit(): void {
        this.signedViewInitialized = true;
    }

    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    /**
     * Loads waiver data based on route parameters using signals
     */
    private async loadWaiverData(params: Params): Promise<void> {
        try {
            this.isLoading.set(true);
            this.hasError.set(false);

            if (!isValidWaiverParams(params)) {
                throw new Error('Invalid or missing waiverSetId parameter');
            }

            const waiverSetId = params.waiverSetId;

            // Load waiver info first
            await firstValueFrom(this._waiverDetailsBL.waiverInfo$);

            // Get waiver sets
            const response = await firstValueFrom(
                this._waiverDetailsBL.getWaiverSet()
            );
            const allWaiverSets =
                this._waiverDetailsBL.getAllWaiverSetsByWaiverSetId(response);
            const filteredWaiverSets =
                this._waiverDetailsBL.filterWaiverSetsByEffectiveDates(
                    allWaiverSets
                );

            // Find the specific waiver set
            const waiverSet = filteredWaiverSets.find(
                (ws) => ws.WaiverSetId.toString() === waiverSetId
            );

            if (waiverSet) {
                this.currentWaiverSet.set(waiverSet);
                this.waiverName.set(waiverSet.Name);
                this.route.snapshot.data['breadcrumb'] = waiverSet.Name;
                this.breadcrumbService.refreshBreadcrumbs(this.route);

                // Load waiver data
                const waiverData = await firstValueFrom(
                    this._waiverDetailsBL.getWaiverData(waiverSet)
                );
                this.waiverData.set(waiverData);

                // Start HTML waiver signing if needed
                if (waiverData?.type === 'html' && !this.isWaiverSigned()) {
                    this.startHtmlWaiverSigning();
                }
            }

            this.isLoading.set(false);
        } catch (error) {
            console.error('Error loading waiver data:', error);
            this.isLoading.set(false);
            this.waiverData.set(null);
            this.hasError.set(true);

            this.errorMessage.set(
                'Failed to load waiver data. Please try again.'
            );
        }
    }

    /**
     * Reactive stream for handling PDF waiver signature submission
     */
    readonly signatureSubmissionResult$ = this.signatureSubmission$.pipe(
        switchMap((signatureBase64: string) => {
            const currentWaiverSet = this.currentWaiverSet();

            if (!currentWaiverSet) {
                console.error('No current waiver set available');
                this.showError();
                return EMPTY;
            }

            if (!signatureBase64 || signatureBase64.trim() === '') {
                console.error('No signature data provided');
                this.showError();
                return EMPTY;
            }

            this.isSigning.set(true);

            return this._signingStateService
                .signPdfWaiver(
                    signatureBase64,
                    currentWaiverSet.WaiverSetId,
                    currentWaiverSet.WaiversContainerDTO[0].WaiverSetDetailId,
                    currentWaiverSet,
                    'Website'
                )
                .pipe(
                    tap((status) => {
                        this.isSigning.set(false);
                        // Don't close the modal yet - keep it open with loading state

                        if (status.success) {
                            this.isWaiverSigned.set(true);
                            this.selectedWaiverIndex.set(-1);

                            if (status.data?.data?.[0]?.WaiverCode) {
                                this.currentWaiverCode.set(
                                    status.data.data[0].WaiverCode
                                );

                                // Load signed waiver content to populate the success popup
                                this._getSignedWaiverContentBL
                                    .getSignedWaiverContentByCode(
                                        status.data.data[0].WaiverCode
                                    )
                                    .subscribe({
                                        next: () => {
                                            const successData =
                                                this._getSignedWaiverContentBL.getWaiverSuccessPopupData();
                                            // Add HTML waiver flag if this is an HTML waiver
                                            if (this.isHtmlWaiverSigning()) {
                                                this.successData.set({
                                                    ...successData,
                                                    isHtmlWaiverSigning: true,
                                                });
                                            } else {
                                                this.successData.set(
                                                    successData
                                                );
                                            }
                                            this.showSuccessPopup.set(true);
                                            // Now close the signature modal after success popup is ready
                                            this.showSignatureModal.set(false);
                                        },
                                        error: (error) => {
                                            console.error(
                                                'Error loading signed waiver content:',
                                                error
                                            );
                                            this.showSignatureModal.set(false);
                                            this.showError();
                                        },
                                    });
                            } else {
                                // No waiver code, close modal and show error
                                this.showSignatureModal.set(false);
                                this.showError();
                            }
                        } else {
                            // Handle non-success responses from API
                            console.error(
                                'API returned non-success status:',
                                status
                            );
                            this.showSignatureModal.set(false);
                            this.showError();
                        }
                    }),
                    catchError((error) => {
                        this.isSigning.set(false);
                        this.showSignatureModal.set(false);

                        console.error(
                            'API error during waiver signing:',
                            error
                        );
                        this.showError();

                        return EMPTY;
                    })
                );
        }),
        takeUntil(this.destroy$)
    );

    /**
     * Shows the signature modal for a specific waiver
     */
    showSignModal(waiverIndex: number): void {
        this.selectedWaiverIndex.set(waiverIndex);
        this.showSignatureModal.set(true);
    }

    /**
     * Closes the signature modal and resets the selected waiver index
     */
    closeSignatureModal(): void {
        this.showSignatureModal.set(false);
        this.selectedWaiverIndex.set(-1);
    }

    /**
     * Handles the signature submission event using reactive approach
     */
    onSignatureSubmitted(signatureBase64: string): void {
        this.signatureSubmission$.next(signatureBase64);
    }

    /**
     * Gets the name of the currently selected waiver
     */
    getSelectedWaiverName(): string {
        return this.waiverName() || 'Sign Waiver';
    }

    /**
     * Navigates to the next step in the waiver process
     */
    proceedToNext(): void {
        this.router.navigate(['/waivers/check-in']);
    }

    /**
     * Handles success popup close
     */
    onSuccessPopupClose(): void {
        this.showSuccessPopup.set(false);
        this.successData.set(undefined);
        this.router.navigate(['/waivers/my-signed-waivers']);
    }

    /**
     * Handles success popup download
     */
    onSuccessPopupDownload(data: {
        waiver: CustomerSignedWaiverDTO;
        type: 'html' | 'pdf';
    }): void {
        if (data.type === 'html') {
            // For HTML waivers, download the first waiver
            const waiverCode = this.currentWaiverCode();
            if (waiverCode) {
                this._getSignedWaiverContentBL.downloadSignedWaiverByCode(
                    waiverCode
                );
            }
        } else {
            // For PDF waivers, download the specific user's waiver
            if (data.waiver.CustomerSignedWaiverId) {
                this._getSignedWaiverContentBL.downloadSignedWaiverForUser(
                    data.waiver.CustomerSignedWaiverId
                );
            }
        }
    }

    /**
     * Handles success popup preview
     */
    onSuccessPopupPreview(data: {
        waiver: CustomerSignedWaiverDTO;
        type: 'html' | 'pdf';
    }): void {
        if (data.type === 'html') {
            // For HTML waivers, preview the first waiver
            const waiverCode = this.currentWaiverCode();
            if (waiverCode) {
                this._getSignedWaiverContentBL.openSignedWaiverPreviewByCode(
                    waiverCode
                );
            }
        } else {
            // For PDF waivers, preview the specific user's waiver
            if (data.waiver.CustomerSignedWaiverId) {
                this._getSignedWaiverContentBL.openSignedWaiverPreviewForUser(
                    data.waiver.CustomerSignedWaiverId
                );
            }
        }
    }

    /**
     * Handles failure popup close
     */
    onFailurePopupClose(): void {
        this.showFailurePopup.set(false);
        this.failureData.set(undefined);
    }

    /**
     * Handles failure popup try again
     */
    onFailurePopupTryAgain(): void {
        this.showFailurePopup.set(false);
        this.failureData.set(undefined);
    }

    /**
     * Starts HTML waiver signing process
     */
    startHtmlWaiverSigning(): void {
        const waiverData = this.waiverData();
        const currentWaiverSet = this.currentWaiverSet();

        if (waiverData && waiverData.type === 'html' && currentWaiverSet) {
            const htmlWaivers: HtmlWaiverData[] = [
                {
                    CustomerWaiverHTMLForm: waiverData.base64Data,
                    SuccessButtonURL: '/success',
                    CancelButtonURL: '/cancel',
                    SignMessage: 'Please sign the waiver',
                    WaiverType: 'HTML',
                },
            ];

            const signingData = {
                waiverSetId: currentWaiverSet.WaiverSetId,
                waiverSetDetailId:
                    currentWaiverSet.WaiversContainerDTO[0].WaiverSetDetailId,
                signatureData: '',
                customerId: this.getCustomerId(),
                relatedCustomerIds: [],
            };

            this._htmlWaiverSigningBL.initializeHtmlWaiverSigning(
                htmlWaivers,
                signingData,
                {
                    onSuccess: (waiverCode?: string) =>
                        this.onHtmlWaiverCompleted(waiverCode),
                    onCancel: () => this.onHtmlWaiverCancelled(),
                    onError: (error: string) =>
                        this.handleHtmlWaiverError(error),
                }
            );

            this._htmlWaiverSigningBL.startHtmlWaiverSigning();
        }
    }

    /**
     * Gets the current customer ID from the userId cookie
     */
    private getCustomerId(): number {
        const userId = this._cookieService.getCookie('userId');
        if (userId) {
            const parsedUserId = parseInt(userId, 10);
            if (!isNaN(parsedUserId)) {
                return parsedUserId;
            }
        }

        console.warn('No valid userId found in cookie, using fallback value');
        return 1;
    }

    /**
     * Handles HTML waiver completion
     */
    onHtmlWaiverCompleted(waiverCode?: string): void {
        this.isWaiverSigned.set(true);

        if (waiverCode) {
            this.currentWaiverCode.set(waiverCode);

            // Load signed waiver content to populate the success popup
            this._getSignedWaiverContentBL
                .getSignedWaiverContentByCode(waiverCode)
                .subscribe(() => {
                    const successData =
                        this._getSignedWaiverContentBL.getWaiverSuccessPopupData();
                    // Add HTML waiver flag since this is an HTML waiver
                    this.successData.set({
                        ...successData,
                        isHtmlWaiverSigning: true,
                    });
                    this.showSuccessPopup.set(true);
                });
        } else {
            // Fallback if no waiver code
            const currentWaiverSet = this.currentWaiverSet();
            if (currentWaiverSet) {
                this.successData.set(
                    this._getSignedWaiverContentBL.getWaiverSuccessPopupData()
                );
                this.showSuccessPopup.set(true);
            }
        }

        this._htmlWaiverSigningBL.resetHtmlWaiverSigning();
    }

    /**
     * Handles HTML waiver cancellation
     */
    onHtmlWaiverCancelled(): void {
        this._htmlWaiverSigningBL.resetHtmlWaiverSigning();
        this.router.navigate(['/waivers/list']);
    }

    /**
     * Handles HTML waiver errors
     */
    handleHtmlWaiverError(error: string): void {
        console.error('HTML Waiver error:', error);

        this._htmlWaiverSigningBL.resetHtmlWaiverSigning();

        this.failureData.set({
            errorMessage: error,
            retryAllowed: true,
        });
        this.showFailurePopup.set(true);
    }

    /**
     * Shows error popup with consistent error handling
     */
    private showError(): void {
        this.failureData.set({
            errorMessage: '',
            retryAllowed: true,
        });
        this.showFailurePopup.set(true);
    }

    /**
     * Renders signed waiver PDF using PDF.js
     */
    private async renderSignedWaiverPdf(): Promise<void> {
        const pdfData = this.signedWaiverPdfData();

        if (!pdfData) {
            console.error('No PDF data available for rendering');
            return;
        }

        // ViewChild is guaranteed to be available since we check signedViewInitialized
        const container = this.signedPdfContainer?.nativeElement;

        if (!container) {
            console.error('Signed PDF container not available');
            return;
        }

        // Use the consolidated service method with proper callback handling
        await this._base64PdfService.renderPdfWithPdfJs(
            pdfData,
            container,
            () => {
                this.isSignedPdfRendering.set(true);
            },
            () => {
                this.isSignedPdfRendering.set(false);
            },
            (error: string) => {
                console.error('Error rendering signed waiver PDF:', error);
                this.isSignedPdfRendering.set(false);
            },
            (pageNumbers: number[]) => {
                this.pdfPageNumbers.set(pageNumbers);
            }
        );
    }
}

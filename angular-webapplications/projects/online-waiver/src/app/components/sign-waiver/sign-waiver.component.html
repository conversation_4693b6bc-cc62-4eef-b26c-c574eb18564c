<div class="flex flex-col gap-y-4 lg:gap-6 md:min-h-[calc(100vh-168px)]">
    <!-- <PERSON> Header -->
    <h1 i18n="sign-waiver.title" class="text-xl md:text-lg font-semibold text-gray-900">
        Sign waivers - {{ waiverName() }}
    </h1>

    <app-participant-card></app-participant-card>

    <div class="w-full">
        <div class="bg-white rounded-4xl shadow-sm border border-gray-100 py-4">
            <div class="pb-2 md:pb-4 px-4">
                <p i18n="sign-waiver.participants" class="text-lg font-medium text-black-900">Read and sign waiver</p>
                <p i18n="sign-waiver.participants-list" class="text-sm text-gray-600">Please read the waiver carefully
                    and sign at the end of the document.
                </p>
            </div>

            <!-- Loading State -->
            @if (isLoadingComputed()) {
            <div class="px-4 py-8 flex items-center justify-center">
                <div class="flex flex-col items-center gap-4">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-500"></div>
                    <p class="text-gray-600 text-sm">Loading waiver data...</p>
                </div>
            </div>
            }

            <!-- Error State -->
            @if (hasError()) {
            <div class="px-4 py-8 flex items-center justify-center">
                <div class="flex flex-col items-center gap-4">
                    <img src="assets/icons/alert.svg" alt="alert" class="w-6 h-6">
                    <p class="text-gray-600 text-sm">{{ errorMessage() }}</p>
                </div>
            </div>
            }

            <!-- Single Waiver Content -->
            @if (waiverData()) {
            <div class="px-4 py-4">
                <!-- Waiver Header -->
                <div class="flex items-center justify-between mb-4">
                    <span class="font-bold text-gray-900 text-lg">
                        {{ waiverName() }}
                    </span>
                    <div class="flex items-center space-x-3 md:mr-12">
                        @if (isWaiverSigned()) {
                        <div
                            class="inline-flex items-center gap-2 px-2 md:px-4 py-2 bg-green-100 border border-green-100 rounded-3xl">
                            <img src="assets/icons/green-tick.svg" alt="tick-circle" class="w-6 h-6">
                            <span class="text-sm text-green-600 font-medium"
                                i18n="sign-waiver.signed-status">Signed</span>
                        </div>
                        } @else {
                        <div
                            class="inline-flex items-center gap-2 px-2 md:px-4 py-2 bg-red-100 border border-red-100 rounded-3xl">
                            <img src="assets/icons/close-red.svg" alt="cross-circle" class="w-6 h-6">
                            <span class="text-sm text-red-700 font-medium" i18n="sign-waiver.not-signed">Not
                                signed</span>
                        </div>
                        }
                    </div>
                </div>

                <!-- HTML Waiver Signing -->
                @if (isHtmlWaiverSigning()) {
                <div class="mt-4">
                    <!-- HTML Waiver Content -->
                    <div class="rounded-4xl w-full overflow-hidden">
                        <div class="p-4">
                            <div class="h-[500px] overflow-scroll tab-content">
                                <div id="waiverhtml" [innerHTML]="currentHtmlWaiverContent() | safeHtml"></div>
                            </div>
                        </div>
                    </div>
                </div>
                } @else {
                <!-- Regular Waiver Preview -->
                <div>
                    <app-preview-waiver [waiverData]="waiverData()" [isSignWaiverContext]="true" />
                </div>
                }

                <!-- Dynamic Button -->
                <div class="mt-2">
                    <ng-container [ngTemplateOutlet]="signCTA"
                        [ngTemplateOutletContext]="{ $implicit: waiverData() }"></ng-container>
                </div>
            </div>
            }

        </div>
    </div>
</div>

<!-- Sign CTA Template -->
<ng-template #signCTA let-waiverData>
    @if (waiverData?.type !== 'html') {
    <div class="flex flex-col items-center md:items-start">
        <lib-button type="primary" size="lg" [fullWidth]="true" (clicked)="showSignModal(0)" class="w-[300px]">
            Sign Waiver
        </lib-button>
    </div>
    }
</ng-template>

<!-- Signature Modal -->
<ng-template #signatureModal>
    <div class="px-6 pb-6">
        <app-signing-pad [isSubmitting]="isSigning() || _getSignedWaiverContentBL.isLoading()"
            (signatureData)="onSignatureSubmitted($event)">
        </app-signing-pad>
    </div>
</ng-template>

<lib-modal [isOpen]="showSignatureModal()" [modalContent]="signatureModal" (closeModal)="closeSignatureModal()"
    [dialogueHeader]="getSelectedWaiverName()">
</lib-modal>

<!-- Success Popup -->
@if (showSuccessPopup()) {
<app-waiver-success-popup [data]="successData()" [isHtmlWaiverSigning]="isHtmlWaiverSigning()"
    (close)="onSuccessPopupClose()" (download)="onSuccessPopupDownload($event)"
    (preview)="onSuccessPopupPreview($event)">
</app-waiver-success-popup>
}

<!-- Failure Popup -->
@if (showFailurePopup()) {
<app-waiver-failure-popup [data]="failureData()" (close)="onFailurePopupClose()" (tryAgain)="onFailurePopupTryAgain()">
</app-waiver-failure-popup>
}

<!-- Signed Waiver Preview Modal -->
<ng-template #signedWaiverPreviewContent>
    <div class="px-5 pb-5">
        @if (_getSignedWaiverContentBL.modalContent()) {
        <p class="text-red-500 text-center min-w-[20vw] min-h-[20vh] flex items-center justify-center">
            {{ _getSignedWaiverContentBL.modalContent() }}
        </p>
        } @else if (signedWaiverPdfData()) {
        <!-- PDF Content using PDF.js canvas rendering -->
        <div #signedPdfContainer
            class="w-full bg-white overflow-y-auto max-h-[85vh] relative min-w-[25vw] min-h-[30vh] ">
            <!-- Loading state -->
            @if (isSignedPdfRendering()) {
            <div class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
                <div class="flex flex-col items-center gap-4">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"></div>
                    <p class="text-gray-600 text-sm">Loading signed waiver PDF...</p>
                </div>
            </div>
            }

            <!-- PDF Pages Container -->
            <div class="flex flex-col gap-4 md:p-4">
                @for (pageNum of pdfPageNumbers(); track pageNum) {
                <div class="pdf-page-container flex justify-center">
                    <canvas [id]="'pdfPage' + pageNum" class="max-w-full h-auto"></canvas>
                </div>
                }
            </div>
        </div>
        } @else {
        <p class="text-gray-500 text-center min-w-[25vw] min-h-[30vh] flex items-center justify-center">
            Loading signed waiver preview...
        </p>
        }
    </div>
</ng-template>

<lib-modal [isOpen]="_getSignedWaiverContentBL.showModal()" [modalContent]="signedWaiverPreviewContent"
    (closeModal)="_getSignedWaiverContentBL.closeModal()" [dialogueHeader]="_getSignedWaiverContentBL.modalTitle()">
</lib-modal>
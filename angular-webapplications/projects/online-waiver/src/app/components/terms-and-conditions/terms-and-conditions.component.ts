/**
 * @fileoverview Terms and conditions component - This component is used to display the PDF/ HTML terms and conditions for the waiver
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-05
 */

import { CommonModule } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    computed,
    effect,
    inject,
    input,
    OnDestroy,
    OnInit,
    output,
    signal,
    ViewChild,
    ElementRef,
    ChangeDetectorRef,
} from '@angular/core';
import { CustomerUIMetadataServiceDL } from 'lib-app-core';
import {
    CheckBoxComponent,
    ModalComponent,
    SkeletonLoaderComponent,
} from 'lib-ui-kit';
import { waiverConstants } from '../../constants/waiver.constant';
import { Base64PdfBLService } from '../../services/buisiness-layer/base64-pdf-bl.service';
import { CustomerUIMetadataServiceBL } from '../../services/buisiness-layer/customer-ui-metadata-bl.service';
import { TermsAndConditionsBL } from '../../services/buisiness-layer/terms-and-condtion-bl.service';
import { WaiverPreviewBLService } from '../../services/business-layer/waiver-preview-bl.service';
import { FormsModule } from '@angular/forms';

@Component({
    selector: 'app-terms-and-conditions',
    imports: [
        CommonModule,
        ModalComponent,
        CheckBoxComponent,
        SkeletonLoaderComponent,
        FormsModule,
    ],
    providers: [
        CustomerUIMetadataServiceDL,
        Base64PdfBLService,
        TermsAndConditionsBL,
        WaiverPreviewBLService,
    ],
    templateUrl: './terms-and-conditions.component.html',
    styleUrl: './terms-and-conditions.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TermsAndConditionsComponent implements OnInit, OnDestroy {
    // Inputs
    hasTermsAndConditions = input<boolean>(false);
    termsAccepted = output<boolean>();
    checked = signal<boolean>(false);

    // ViewChild reference for PDF container
    @ViewChild('pdfContainer', { static: false })
    pdfContainer!: ElementRef<HTMLDivElement>;

    // Services
    protected readonly customerUIMetadataServiceBL = inject(
        CustomerUIMetadataServiceBL
    );
    protected readonly termsAndConditionsBL = inject(TermsAndConditionsBL);
    private readonly _waiverPreviewService = inject(WaiverPreviewBLService);
    private readonly _base64PdfService = inject(Base64PdfBLService);

    // Expose signals from the business layer service
    readonly showModal = this.termsAndConditionsBL.showModal;
    readonly modalContent = this.termsAndConditionsBL.modalContent;
    readonly modalTitle = this.termsAndConditionsBL.modalTitle;

    // PDF rendering state
    currentPdfData = signal<string | null>(null);
    isPdfRendering = signal(false);
    pdfPageNumbers = signal<number[]>([]);

    readonly showTermsAndConditions = computed(() => {
        return (
            this.hasTermsAndConditions() &&
            !!(
                this.termsAndConditionsBL.termsAndConditions() ||
                this.termsAndConditionsField()
            ) &&
            !this.termsAndConditionsBL.loading()
        );
    });

    constructor(private _cdr: ChangeDetectorRef) {
        effect(() => {
            this.termsAccepted.emit(this.checked());
        });

        // Effect to handle PDF rendering when modal opens
        effect(() => {
            const isOpen = this.showModal();
            const pdfData = this.currentPdfData();

            if (isOpen && pdfData) {
                // Small delay to ensure modal is rendered
                setTimeout(() => {
                    this.renderPdfWithPdfJs();
                }, 100);
            }
        });
    }

    ngOnInit() {
        this.termsAndConditionsBL.loadRichContents();
    }

    readonly termsAndConditionsField = computed(() => {
        const metadata = this.customerUIMetadataServiceBL.customerUIMetadata();
        return (
            metadata.find(
                (field) =>
                    field.CustomerFieldName ===
                    waiverConstants.TERMS_AND_CONDITIONS
            ) || null
        );
    });

    readonly termsAndConditionsHTML = computed(() => {
        const termsField = this.termsAndConditionsField();
        if (!termsField?.EntityFieldCaption) {
            return '';
        }

        // Process the HTML to add classes to anchor tags
        return this.addClassesToAnchorTags(termsField.EntityFieldCaption);
    });

    /**
     * Adds `underline` and `text-secondary-blue` classes to all <a> tags in the given HTML string.
     * Hydration-safe: operates entirely on the string without browser-only DOM APIs,
     * so SSR and CSR output will be identical.
     * @param html - The HTML string to add classes to
     * @returns The HTML string with the classes added to the <a> tags
     */
    private addClassesToAnchorTags(html: string): string {
        if (!html) return html;

        const classToAdd = 'underline text-secondary-blue text-sm';

        // Step 1: Add classes if the <a> tag has no class attribute
        let updatedHtml = html.replace(
            /<a(?![^>]*\bclass=)/g,
            `<a class="${classToAdd}"`
        );

        // Step 2: Append classes if a class attribute already exists
        updatedHtml = updatedHtml.replace(
            /<a([^>]*)class="([^"]*)"/g,
            (_match, otherAttrs, existingClasses) =>
                `<a${otherAttrs}class="${existingClasses} ${classToAdd}"`
        );

        return updatedHtml;
    }

    /**
     * Closes the modal
     */
    onModalClose(): void {
        this.termsAndConditionsBL.closeModal();
        this.currentPdfData.set(null);
        this.pdfPageNumbers.set([]);
    }

    /**
     * Prevents the default behavior of the event and opens the terms and conditions modal
     *
     * @param event - The event to prevent
     */
    onTermsLinkClick(event: Event): void {
        event.preventDefault();
        event.stopPropagation();
        this.onTermsClick();
    }

    /**
     * Opens the terms and conditions modal
     */
    onTermsClick(): void {
        const { content } = this.termsAndConditionsBL.termsAndConditionsData();
        if (content?.Data) {
            this.currentPdfData.set(content.Data);
            this.termsAndConditionsBL.showModal.set(true);
        }
    }

    /**
     * Renders PDF using PDF.js canvas rendering
     */
    async renderPdfWithPdfJs(): Promise<void> {
        const pdfData = this.currentPdfData();

        if (!pdfData) {
            return;
        }

        // ViewChild is guaranteed to be available since we check showModal state
        const container = this.pdfContainer?.nativeElement;

        if (!container) {
            console.error('PDF container not available');
            return;
        }

        // Use the consolidated service method with proper callback handling
        await this._base64PdfService.renderPdfWithPdfJs(
            pdfData,
            container,
            () => {
                this.isPdfRendering.set(true);
            },
            () => {
                this.isPdfRendering.set(false);
            },
            (error: string) => {
                console.error('Error rendering PDF:', error);
                this.isPdfRendering.set(false);
            },
            (pageNumbers: number[]) => {
                this.pdfPageNumbers.set(pageNumbers);
            }
        );
    }

    /**
     * Closes the modal when the component is destroyed
     */
    ngOnDestroy(): void {
        this.termsAndConditionsBL.closeModal();
    }
}

@if (showTermsAndConditions()) {
<div class="flex gap-2 items-center text-sm">
    <lib-checkbox [id]="'terms-checkbox'" customClass="w-[18px] h-[18px] self-end" [(ngModel)]="checked"></lib-checkbox>
    <label for="terms-checkbox">
        @if (termsAndConditionsBL.termsAndConditions()) {
        <!-- RichContents has high priority - show button that opens modal -->
        <span i18n="terms-and-conditions.i-agree-to-the-text">I agree to the </span>
        <button type="button" (click)="onTermsClick()" class="underline text-secondary-blue">
            <span i18n="terms-and-conditions.terms-and-conditions-button" class="text-sm">
                {{ termsAndConditionsBL.termsAndConditions()?.ContentName || "Terms and Conditions" }}
            </span>
        </button>
        } @else if (termsAndConditionsField()?.EntityFieldCaption) {
        <!-- CustomerUIMetadata - display with working links using [innerHTML] -->
        <span [innerHTML]="termsAndConditionsHTML()"></span>
        }
    </label>
</div>
} @else{
<lib-skeleton-loader skeletonClass="animate-pulse h-5 w-80 bg-surface-lightest rounded-md" />
}

<!-- Modal for displaying terms and conditions -->
<lib-modal [isOpen]="showModal()" [dialogueHeader]="modalTitle()" (closeModal)="onModalClose()"
    [modalContent]="pdfViewerContent">
</lib-modal>

<!-- PDF Viewer Modal content -->
<ng-template #pdfViewerContent>
    <div class="px-5 pb-5">
        @if (currentPdfData()) {
        <!-- PDF Content using PDF.js canvas rendering -->
        <div #pdfContainer class="w-full bg-white overflow-y-auto max-h-[85vh] relative">
            <!-- Loading state -->
            @if (isPdfRendering()) {
            <div class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
                <div class="flex flex-col items-center gap-4">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"></div>
                    <p class="text-gray-600 text-sm">Loading terms and conditions PDF...</p>
                </div>
            </div>
            }

            <!-- PDF Pages Container -->
            <div class="flex flex-col gap-4 p-4">
                @for (pageNum of pdfPageNumbers(); track pageNum) {
                <div class="pdf-page-container flex justify-center">
                    <canvas [id]="'pdfPage' + pageNum" class="max-w-full h-auto"></canvas>
                </div>
                }
            </div>
        </div>
        } @else {
        <p class="text-red-500 text-center min-w-[20vw] min-h-[20vh] flex items-center justify-center">
            No PDF found, please contact support.
        </p>
        }
    </div>
</ng-template>
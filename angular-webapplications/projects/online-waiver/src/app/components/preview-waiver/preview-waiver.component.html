<div class="px-4 pb-4 md:pb-6 md:px-6 lg:px-8 min-w-md -z-10">
    <!-- Loading State -->
    @if (isLoading()) {
    <div
        class="bg-surface-lighter rounded-4xl w-full overflow-hidden min-h-[75vh] md:min-h-[85vh] flex items-center justify-center">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p i18n="preview-waiver.loading-waiver-preview" class="text-gray-600 font-medium">Loading waiver
                preview...</p>
        </div>
    </div>
    } @else if (hasError()) {
    <!-- Error State -->
    <div
        class="bg-surface-lighter rounded-4xl w-full overflow-hidden min-h-[70vh] md:min-h-[80vh] min-w-[50vw] flex items-center justify-center">
        <div class="text-center">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <img src="assets/icons/error-warning.svg" alt="Warning" class="w-8 h-8" />
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2" i18n="preview-waiver.no-waiver-found-title">No Waiver
                Found</h3>
            <p class="text-gray-600 mb-4" i18n="preview-waiver.no-waiver-found-description">The waiver content could not
                be loaded.</p>
            <p class="text-sm text-gray-500" i18n="preview-waiver.contact-support-message">Please contact support for
                assistance.</p>
        </div>
    </div>
    } @else {
    <!-- Content Display -->
    <div class="rounded-4xl w-full overflow-hidden flex justify-center">
        <div class="w-full">
            <!-- HTML Content using innerHTML -->
            @if (isHtmlContent()) {
            <div [innerHTML]="htmlContent()"
                class="min-h-[70vh] md:min-h-[75vh] w-full p-4 bg-white overflow-y-auto max-h-[75vh]">
            </div>
            } @else if (isPdfContent()) {
            <!-- PDF Content using ngx-extended-pdf-viewer -->
            <div class="min-h-[50vh] md:min-h-[70vh] bg-white max-w-5xl pdf-container"
                [attr.contents]="isSignWaiverContext() ? 'sign-waiver' : null">
                @if (pdfDataUrl()) {
                <ngx-extended-pdf-viewer [src]="pdfDataUrl()!" [height]="pdfViewerHeight()" [showToolbar]="false"
                    [showSidebarButton]="false" [showFindButton]="false" [showPagingButtons]="false"
                    [showZoomButtons]="false" [showPresentationModeButton]="false" [showOpenFileButton]="false"
                    [showPrintButton]="false" [showDownloadButton]="false" [showSecondaryToolbarButton]="false"
                    [showRotateButton]="false" [showHandToolButton]="false" [showSpreadButton]="false"
                    [showPropertiesButton]="false" [textLayer]="true">
                </ngx-extended-pdf-viewer>
                } @else {
                <div class="flex items-center justify-center h-full">
                    <p class="text-gray-600">No PDF data available</p>
                </div>
                }
            </div>
            } @else {
            <!-- No content type detected -->
            <div class="min-h-[70vh] md:min-h-[80vh] w-full p-4 bg-white flex items-center justify-center">
                <div class="text-center">
                    <p class="text-gray-600">No content type detected or content is empty.</p>
                </div>
            </div>
            }
        </div>
    </div>
    }
</div>
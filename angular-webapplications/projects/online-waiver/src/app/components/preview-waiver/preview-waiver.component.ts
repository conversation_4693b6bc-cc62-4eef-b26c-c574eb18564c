/**
 * @fileoverview
 * Provides the preview of selected waiver set details includes the preview of HTML and Pdf Form waivers
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-09-18
 */

import {
    Component,
    input,
    computed,
    inject,
    AfterViewInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { DomSanitizer } from '@angular/platform-browser';
import { WaiverPreviewData } from '../../interface/waiver.interface';
import { NgxExtendedPdfViewerModule } from 'ngx-extended-pdf-viewer';

@Component({
    selector: 'app-preview-waiver',
    standalone: true,
    imports: [CommonModule, NgxExtendedPdfViewerModule],
    templateUrl: './preview-waiver.component.html',
    styleUrl: './preview-waiver.component.scss',
})
export class PreviewWaiverComponent implements AfterViewInit {
    waiverData = input<WaiverPreviewData | null>(null);
    isSignWaiverContext = input<boolean>(false);
    private readonly _sanitizer = inject(DomSanitizer);

    /**
     * Single computed property to get waiver data once
     */
    readonly _waiverData = computed(() => {
        return this.waiverData();
    });

    /**
     * Computed property that returns the safe HTML content for innerHTML
     * Handles HTML data with proper base64 decoding
     */
    readonly htmlContent = computed(() => {
        const data = this._waiverData();

        if (data && data.type === 'html' && data.base64Data) {
            try {
                const decodedHtml = atob(data.base64Data);
                return this._sanitizer.bypassSecurityTrustHtml(decodedHtml);
            } catch (error) {
                return this._sanitizer.bypassSecurityTrustHtml(
                    '<p class="text-red-500">Error loading HTML content</p>'
                );
            }
        }
        return this._sanitizer.bypassSecurityTrustHtml('');
    });

    /**
     * Computed property to determine if content is loading
     */
    readonly isLoading = computed(() => {
        const data = this._waiverData();
        return !data || data === null;
    });

    /**
     * Computed property to check if content is HTML type
     */
    readonly isHtmlContent = computed(() => {
        const data = this._waiverData();
        return data && data.type === 'html';
    });

    /**
     * Computed property to check if content is PDF type
     */
    readonly isPdfContent = computed(() => {
        const data = this._waiverData();
        return data && data.type === 'pdf';
    });

    /**
     * Computed property to check if there's an error (empty base64 data)
     */
    readonly hasError = computed(() => {
        const data = this._waiverData();

        // Show error if we have data but it's invalid
        if (data !== null && data !== undefined) {
            // For HTML type, show error if base64Data is empty
            if (
                data.type === 'html' &&
                (!data.base64Data || data.base64Data.trim() === '')
            ) {
                return true;
            }
            // For PDF type, show error if base64Data is empty
            if (
                data.type === 'pdf' &&
                (!data.base64Data || data.base64Data.trim() === '')
            ) {
                return true;
            }
        }

        return false;
    });

    /**
     * Initialize component after view is initialized
     */
    ngAfterViewInit(): void {
        // Configure PDF.js worker path for ngx-extended-pdf-viewer
        if (typeof window !== 'undefined' && (window as any).pdfjsLib) {
            (window as any).pdfjsLib.GlobalWorkerOptions.workerSrc =
                '/assets/js/pdf.worker.min.js';
        }
    }

    /**
     * Get responsive height for PDF viewer
     */
    readonly pdfViewerHeight = computed(() => {
        // Only apply responsive height for sign-waiver context
        if (this.isSignWaiverContext()) {
            // Check if we're on mobile (you can adjust this breakpoint)
            if (typeof window !== 'undefined' && window.innerWidth < 768) {
                return '50vh'; // Mobile height for sign-waiver
            }
            return '75vh'; // Desktop height for sign-waiver
        } else if (typeof window !== 'undefined' && window.innerWidth < 768) {
            return '60vh'; // Mobile height for normal usage
        }
        return '75vh';
    });

    /**
     * Get PDF data URL for ngx-extended-pdf-viewer
     */
    pdfDataUrl(): string | null {
        const data = this._waiverData();

        if (data && data.type === 'pdf' && data.base64Data) {
            // Clean the base64 data - remove any existing data URL prefix
            let cleanBase64 = data.base64Data;
            if (cleanBase64.startsWith('data:application/pdf;base64,')) {
                cleanBase64 = cleanBase64.replace(
                    'data:application/pdf;base64,',
                    ''
                );
            }

            const dataUrl = `data:application/pdf;base64,${cleanBase64}`;
            return dataUrl;
        }
        return null;
    }
}

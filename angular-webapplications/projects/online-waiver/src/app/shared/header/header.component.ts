/**
 * @fileoverview HeaderComponent extends the BaseComponent and provides specific functionality for the app's header
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-09-18
 */
import { CommonModule, isPlatformBrowser } from '@angular/common';
import {
    Component,
    HostListener,
    Inject,
    PLATFORM_ID,
    inject,
    signal,
    computed,
    AfterViewInit,
    OnDestroy,
    ChangeDetectorRef,
} from '@angular/core';
import { Router, RouterLink, RouterLinkActive } from '@angular/router';
// import { HeaderTheme } from '../../../../../common-config/theme-env';
import {
    getTemplateUrl,
    ClientHeaderComponent,
    SiteContextService,
} from 'lib-app-core';
import { HeaderTheme } from '@theme/waiver/waiver.theme';
import { DropdownMenuComponent } from '../dropdown-menu/dropdown-menu.component';
import { MobileSidebarComponent } from '../mobile-sidebar/mobile-sidebar.component';
import { AuthService } from '../../core/auth.service';
import { TextInputComponent } from 'lib-ui-kit';
import { LanguageSelectionComponent } from '../../components/language-selection/language-selection.component';
import { FaqComponent } from '../../components/faq/faq.component';
@Component({
    standalone: true,
    selector: 'app-header',
    imports: [
        RouterLink,
        RouterLinkActive,
        CommonModule,
        DropdownMenuComponent,
        MobileSidebarComponent,
        ClientHeaderComponent,
        TextInputComponent,
        LanguageSelectionComponent,
        FaqComponent,
    ],
    templateUrl: getTemplateUrl(HeaderTheme, 'header'),
    styleUrls: ['./header.component.scss'],
})
export class HeaderComponent implements AfterViewInit, OnDestroy {
    readonly authService = inject(AuthService);
    readonly router = inject(Router);
    readonly siteContextService = inject(SiteContextService);
    readonly showFaq = signal(false);
    private readonly cdr = inject(ChangeDetectorRef);

    toggleFaq() {
        this.showFaq.set(!this.showFaq());
    }

    isBrowser = false;
    private readonly isModalOpenSignal = signal(false);
    private mutationObserver: MutationObserver | null = null;

    constructor(@Inject(PLATFORM_ID) private platformId: Object) {
        this.isBrowser = isPlatformBrowser(this.platformId);
    }

    ngAfterViewInit() {
        if (this.isBrowser) {
            this.setupModalObserver();
        }
    }

    ngOnDestroy() {
        if (this.mutationObserver) {
            this.mutationObserver.disconnect();
            this.mutationObserver = null;
        }
    }

    private setupModalObserver() {
        // Initial check
        this.updateModalState();

        // Set up mutation observer to watch for class changes
        this.mutationObserver = new MutationObserver(() => {
            this.updateModalState();
        });

        // Observe the document body for attribute changes
        this.mutationObserver.observe(document.body, {
            attributes: true,
            attributeFilter: ['class'],
        });
    }

    private updateModalState() {
        const hasModalOpen = document.querySelector('.modal-open') !== null;
        this.isModalOpenSignal.set(hasModalOpen);
        this.cdr.detectChanges();
    }

    get isModalOpen() {
        return this.isModalOpenSignal();
    }

    // Close Mobile Sidebar
    @HostListener('document:keydown.escape', ['$event'])
    onEscapePress() {
        if (this.isBrowser) {
            this.mobileSidebarOpen = false;
        }
    }

    navItems = [
        {
            label: $localize`:header.home@@header.home:Home`,
            icon: '/assets/icons/home.svg',
            link: '/',
        },
        {
            label: $localize`:header.waivers@@header.waivers:Waivers`,
            icon: '/assets/icons/preview-black.svg',
            link: '/waivers/list',
        },
    ];

    mobileSidebarOpen = false;

    accountMenuItems = [
        {
            label: $localize`:header.my-profile@@header.my-profile:My Profile`,
            link: '/my-accounts/my-profile',
        },
        {
            label: $localize`:header.my-cards@@header.my-cards:My Cards`,
            link: '/my-accounts/my-cards',
        },
        {
            label: $localize`:header.my-subscriptions@@header.my-subscriptions:My Subscriptions`,
            link: '/my-accounts/my-subscriptions',
        },
        {
            label: $localize`:header.my-signed-waivers@@header.my-signed-waivers:My Signed Waivers`,
            link: '/waivers/my-signed-waivers',
        },
        {
            label: $localize`:header.my-relations@@header.my-relations:My Relations`,
            link: '/waivers/my-relations',
        },
        {
            label: $localize`:header.my-orders@@header.my-orders:My Orders`,
            link: '/my-accounts/my-orders',
        },
        {
            label: $localize`:header.change-password@@header.change-password:Change Password`,
            link: '/my-accounts/change-password',
        },
    ];

    // Toggle Mobile Sidebar
    toggleMobileSidebar(): void {
        this.mobileSidebarOpen = !this.mobileSidebarOpen;

        // Mobile sidebar toggle logic
        // Note: Dropdown state is now managed internally by the dropdown component
    }

    closeSideBar() {
        this.mobileSidebarOpen = false;
    }

    // Note: Account dropdown state is now managed internally by the dropdown component
    // No need for external state management
}

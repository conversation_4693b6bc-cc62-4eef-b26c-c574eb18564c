import { CommonModule } from '@angular/common';
import {
    Component,
    input,
    signal,
    TemplateRef,
    inject,
    contentChild,
} from '@angular/core';
import { Router, RouterLink, RouterLinkActive } from '@angular/router';
import { ClickOutsideDirective } from 'lib/lib-ui-kit/src/lib/directives/click-outside.directive';

export interface DropdownItem {
    label: string;
    link?: string;
    icon?: string;
    value?: string;
    action?: () => void;
}

@Component({
    selector: 'app-dropdown-menu',
    imports: [
        CommonModule,
        RouterLink,
        RouterLinkActive,
        ClickOutsideDirective,
    ],
    templateUrl: './dropdown-menu.component.html',
    styleUrl: './dropdown-menu.component.scss',
})
export class DropdownMenuComponent {
    items = input<DropdownItem[]>([]);
    alignRight = input<boolean>(false);
    customClass = input<string | null>(null);
    size = input<'sm' | 'md' | 'lg'>('md');
    defaultSelection = input<DropdownItem | null>(null);
    width = input<string | null>(null);
    selectedItem = signal<DropdownItem | null>(null);
    readonly router = inject(Router);

    // Template support inputs
    customTemplate = input<TemplateRef<any> | null>(null);
    templateContext = input<any>(null);
    useCustomTemplate = input<boolean>(false);
    customHeader = input<string | null>(null);
    // Internal state management
    readonly isOpen = signal<boolean>(false);

    // Content projection for trigger button
    triggerButton = contentChild('triggerButton');

    toggleDropdown(): void {
        this.isOpen.set(!this.isOpen());
    }

    closeDropdown(): void {
        this.isOpen.set(false);
    }

    onItemClick(item: DropdownItem): void {
        item.action?.();
        item.link && this.router.navigate([item.link]);
        this.closeDropdown();
    }

    isSelected(item: DropdownItem): boolean {
        if (this.selectedItem()) {
            return this.selectedItem()?.label === item.label;
        } else if (this.defaultSelection()) {
            return this.defaultSelection()?.label === item.label;
        }
        return false;
    }

    get customClasses() {
        const sizeClasses = {
            sm: 'p-3 md:p-3 md:pb-1',
            md: 'p-4 md:p-4 md:pb-3',
            lg: 'p-5 md:p-6 md:pb-4'
        }[this.size()] || '';

        const customClass = this.customClass();
        const widthClass = this.width() || 'w-max';

        return [sizeClasses, customClass, widthClass].filter(Boolean).join(' ');
    }
}

<div libClickOutside (clickOutside)="closeDropdown()" [class.relative]="isOpen()">
  <!-- <PERSON><PERSON> (Content Projection) -->
  <div (click)="toggleDropdown()">
    <ng-content select="[triggerButton]"></ng-content>
  </div>

  <!-- Dropdown Menu -->
  @if (isOpen()) {
  <div
    class="absolute mt-2 bg-surface-white rounded-4xl border border-surface z-20 overflow-hidden text-sm leading-5 shadow-lg"
    [class.right-0]="alignRight()" tabindex="0" [ngClass]="customClasses">
    <div class="max-h-[300px] overflow-y-auto">
      @if (useCustomTemplate() && customTemplate()) {
      <!-- Custom Template Content with Optional Header -->
      @if (customHeader()) {
      <div class="pb-3 md:pb-4">
        <h3 class="text-sm font-medium text-black-900">{{ customHeader() }}</h3>
      </div>
      }
      <ng-container *ngTemplateOutlet="customTemplate()!; context: templateContext()">
      </ng-container>
      } @else {
      <!-- Default Header-based Items -->
      @for (item of items(); track $index) {
      <ng-container>
        @if (item.link) {
        <a [routerLink]="item.link" [routerLinkActive]="[
                            'bg-surface-lightest',
                            'text-secondary-blue',
                        ]"
          class="flex items-center min-w-32 text-left p-1.5 mb-2 text-sm text-primary rounded-lg hover:bg-surface-lightest"
          (click)="onItemClick(item)" (touchstart.passive)="onItemClick(item)">
          @if (item.icon) {
          <img [src]="item.icon" class="w-5 h-5 mr-3" [alt]="item.label" />
          }
          {{ item.label }}
        </a>
        }
        @if (!item.link) {
        <button type="button"
          class="flex items-center min-w-32 text-left p-1.5 mb-2 text-sm text-primary rounded-lg hover:bg-surface-lightest"
          (click)="onItemClick(item)" (touchstart.passive)="onItemClick(item)">
          @if (item.icon) {
          <img [src]="item.icon" class="w-5 h-5 mr-3" [alt]="item.label" />
          }
          {{ item.label }}
        </button>
        }
      </ng-container>
      }
      }
    </div>
  </div>
  }
</div>
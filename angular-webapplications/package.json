{"name": "ng-web-workspace", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:prod:online-waiver": "ng build online-waiver --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:online-waiver": "nodemon dist/online-waiver/server/server.mjs", "serve:ssr:online-waiver-debug": "node --inspect dist/online-waiver/server/server.mjs", "extract-i18n:online-waiver": "ng extract-i18n --output-path projects/online-waiver/src/locale online-waiver"}, "private": true, "dependencies": {"@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/platform-server": "^19.2.0", "@angular/router": "^19.2.0", "@angular/ssr": "^19.2.1", "@types/signature_pad": "^2.3.6", "crypto-es": "3.0.0", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "express": "^4.18.2", "jsencrypt": "^3.5.4", "ngx-extended-pdf-viewer": "^25.0.1", "rxjs": "~7.8.0", "signature_pad": "^5.0.10", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.1", "@angular/cli": "^19.2.0", "@angular/compiler-cli": "^19.2.0", "@angular/localize": "^19.2.14", "@tailwindcss/aspect-ratio": "^0.4.2", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "autoprefixer": "^10.4.20", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "ng-packagr": "^19.2.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "~5.7.2"}}
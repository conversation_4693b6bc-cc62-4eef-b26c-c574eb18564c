/**
 * @fileoverview 
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-03-12
 */

import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
    providedIn: 'root',
})
export abstract class ExecutionContextBaseInterceptor implements HttpInterceptor {
    token: string | null = null;

    private excludedUrls: string[] = [
        '/api/Login/Secure/AuthenticateSystemUsers',
        '/api/Login/SecondsSinceEpoch',
        '/api/Login/PublicKey',
    ];

    protected abstract getWebApiToken(): string | null;

    constructor() { }

    intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        // console.log('Auth Request URL: ' + req.url);

        // Check if the request URL matches any of the excluded URLs
        if (this.excludedUrls.some(url => req.url.includes(url))) {
            return next.handle(req);
        }

        this.token = this.getWebApiToken();
        const newReq = req.clone({
            headers: req.headers.set('Authorization', `Bearer ${this.token}`),
        });

        return next.handle(newReq);

    }
}

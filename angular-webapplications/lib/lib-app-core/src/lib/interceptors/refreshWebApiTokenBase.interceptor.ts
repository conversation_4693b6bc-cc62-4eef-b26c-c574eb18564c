/**
 * @fileoverview Refresh Web API Token Base Interceptor to handle 401 errors by logging out the user and redirecting to site selection to start fresh execution context
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-09-03
 */
import {
    HttpErrorResponse,
    HttpEvent,
    HttpHandler,
    HttpInterceptor,
    HttpRequest,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
    catchError,
    Observable,
    throwError,
} from 'rxjs';

@Injectable()
export abstract class RefreshWebApiTokenBaseInterceptor implements HttpInterceptor {
    protected abstract handleLogout(): void;
    intercept(
        req: HttpRequest<any>,
        next: HttpHandler
    ): Observable<HttpEvent<any>> {
        return next.handle(req).pipe(
            catchError((error: HttpErrorResponse) => {
                // Handle 401 Unauthorized errors by logging out and redirecting to site selection
                if (error.status === 401) {
                    this.handle401Error();
                    // Return an observable that completes immediately after logout and redirect
                    return new Observable<HttpEvent<any>>(subscriber => {
                        subscriber.complete();
                    });
                }

                // For all other errors, we re-throw them.
                return throwError(() => error);
            })
        );
    }

    /**
     * Handles 401 errors by logging out the user and redirecting to site selection to start fresh execution context
     */
    private handle401Error(): void {
        this.handleLogout();    
    }
}

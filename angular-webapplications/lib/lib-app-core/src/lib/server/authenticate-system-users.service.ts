/**
 * @file AuthenticateSystemUsersService.ts
 * @description Service to authenticate system users by sending login credentials
 * to a remote API with encryption support.
 * <AUTHOR>
 * @date September 2, 2025
 */

import CryptoES, { WordArray } from 'crypto-es';
import { JSEncrypt } from 'jsencrypt';

export interface EncryptedSystemUsersRequest {
    EncryptedLoginId: string;
    EncryptedPassword: string;
    Application: string;
    Version: string;
    Identifier: string;
    SiteId: number;
    EncryptedMachineName: string;
    EncryptedKeyMaterial: string;
    LanguageId: string;
}

export class AuthenticateSystemUsersService {
    private baseApiUrl: string = process.env['PARAFAIT_API_BASEURL'] || '';
    private loginId: string;
    private password: string;
    private applicationName: string;
    private applicationVersion: string;
    private applicationIdentifier: string;

    private aesKey: WordArray | null = null;
    private jsEncrypt: JSEncrypt | null = null;
    private secondsSinceEpoch: number | null = null;

    constructor() {
        this.loginId = process.env['LOGIN_ID'] || '';
        this.password = process.env['PASSWORD'] || '';
        this.applicationName = process.env['APPLICATION_NAME'] || '';
        this.applicationVersion = process.env['APPLICATION_VERSION'] || '';
        this.applicationIdentifier =
            process.env['APPLICATION_IDENTIFIER'] || '';
    }

    private async initializeEncryption(siteId: number): Promise<void> {
        this.jsEncrypt = new JSEncrypt();

        const [timeResponse, keyPem] = await Promise.all([
            this.getSecondsSinceEpoch(),
            this.getPublicKey(siteId),
        ]);

        this.secondsSinceEpoch = timeResponse;
        if (!keyPem)
            throw new Error('Public key could not be fetched or is empty.');
        this.jsEncrypt.setPublicKey(keyPem);
    }

    private async getSecondsSinceEpoch(): Promise<number> {
        const url = `${this.baseApiUrl}/Login/SecondsSinceEpoch`;
        const res = await fetch(url);
        if (!res.ok)
            throw new Error(
                `Failed to fetch server time: ${res.status} ${res.statusText}`
            );
        const result = await res.json();
        return result.data;
    }

    private async getPublicKey(siteId: number): Promise<string> {
        const url = `${this.baseApiUrl}/Login/PublicKey`;
        const params = new URLSearchParams({
            siteId: siteId.toString(),
            application: this.applicationName,
            version: this.applicationVersion,
            identifier: this.applicationIdentifier,
            format: 'PEM',
        });

        const res = await fetch(`${url}?${params}`);
        if (!res.ok)
            throw new Error(
                `Failed to fetch public key: ${res.status} ${res.statusText}`
            );
        const result = await res.json();
        return result.data;
    }

    private generateAESKey(bits: number = 256): void {
        this.aesKey = CryptoES.lib.WordArray.random(bits / 8);
    }

    private aesEncrypt(message: string): string {
        if (!this.aesKey) throw new Error('AES key not initialized');

        // Using static IV to match server implementation (CBC + PKCS7).
        const iv = CryptoES.enc.Hex.parse('00000000000000000000000000000000');
        const encrypted = CryptoES.AES.encrypt(message, this.aesKey, {
            iv,
            mode: CryptoES.mode.CBC,
            padding: CryptoES.pad.Pkcs7,
        });

        if (!encrypted.ciphertext)
            throw new Error('AES encryption failed: ciphertext is undefined');
        // Server expects raw ciphertext in Base64 (no OpenSSL header).
        return encrypted.ciphertext.toString(CryptoES.enc.Base64);
    }

    private rsaEncrypt(message: string): string {
        if (!this.jsEncrypt) throw new Error('JSEncrypt not initialized');
        const encrypted = this.jsEncrypt.encrypt(message);
        if (encrypted === false) {
            throw new Error(
                'RSA encryption failed. The message may be too long for the key size.'
            );
        }
        return encrypted;
    }

    /**
     * Authenticates a system user by sending an encrypted POST request to the API.
     * NOTE: Return type is unknown because it’s the API response, not the request payload.
     */
    public async authenticate(
        machineName: string,
        languageId: string,
        siteId: number
    ): Promise<unknown> {
        await this.initializeEncryption(siteId);
        this.generateAESKey();
        if (!this.aesKey) throw new Error('AES key was not generated');
        if (this.secondsSinceEpoch == null)
            throw new Error('Server time not initialized');

        // Base64(AES key) + '|' + secondsSinceEpoch, then RSA encrypt.
        const base64AesKey = this.aesKey.toString(CryptoES.enc.Base64);
        const keyMaterial = `${base64AesKey}|${this.secondsSinceEpoch}`;
        const encryptedKeyMaterial = this.rsaEncrypt(keyMaterial);

        // Encrypt fields with AES (Base64 ciphertext).
        const encryptedLoginId = this.aesEncrypt(this.loginId);
        const encryptedPassword = this.aesEncrypt(this.password);
        const encryptedMachineName = this.aesEncrypt(machineName);

        const payload: EncryptedSystemUsersRequest = {
            EncryptedLoginId: encryptedLoginId,
            EncryptedPassword: encryptedPassword,
            Application: this.applicationName,
            Version: this.applicationVersion,
            Identifier: this.applicationIdentifier,
            SiteId: siteId,
            EncryptedMachineName: encryptedMachineName,
            EncryptedKeyMaterial: encryptedKeyMaterial,
            LanguageId: languageId,
        };

        const authApiUrl = `${this.baseApiUrl}/Login/Secure/AuthenticateSystemUsers`;
        const response = await fetch(authApiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                Accept: 'application/json',
            },
            body: JSON.stringify(payload),
        });

        if (!response.ok) {
            const errorBody = await response.text();
            // Keep this log in place while testing; remove in prod to avoid leaking details.
            console.error('Authentication API Error:', errorBody);
            throw new Error(
                `HTTP error! Status: ${response.status} - ${response.statusText}`
            );
        }

        return response.json();
    }
}

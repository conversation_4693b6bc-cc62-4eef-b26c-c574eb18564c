/**
 * @fileoverview
 *
 * <AUTHOR>
 * @version 1.0.0
 */

import { isPlatformBrowser, isPlatformServer } from '@angular/common';
import { Injectable, PLATFORM_ID, inject } from '@angular/core';
import { Router } from '@angular/router';
import {
    SecurityTokensServiceDL,
    CookieService,
    ParafaitDefaultContainerService,
    SiteContextService,
    SiteViewsServiceDL,
    areEqual,
} from 'lib-app-core';
import { firstValueFrom } from 'rxjs';
import { AuthenticateSystemUsersDTOModel } from '../models/authenticate-system-users-dto.model';
import { AuthenticateSystemUsersServerServiceDL } from './data-layer/authenticate-system-users-server-dl.service';
import { EncryptedSystemUsersConfig } from 'lib/lib-auth/src/lib/services/login-encryption.service';

@Injectable({
    providedIn: 'root',
})
export abstract class AppInitBaseService {
    private _authenticateSystemUsersServerServiceDL = inject(
        AuthenticateSystemUsersServerServiceDL
    );
    private _securityTokensServiceDL = inject(SecurityTokensServiceDL);
    private _siteViewsServiceDL = inject(SiteViewsServiceDL);
    private _siteContextService = inject(SiteContextService);
    private _parafaitDefaultContainerServiceDL = inject(
        ParafaitDefaultContainerService
    );
    private _platformId = inject(PLATFORM_ID);
    private _cookieService = inject(CookieService);
    private _webApiToken: string | null = null;
    private _router = inject(Router);
    protected _siteId: number = this._siteContextService.siteId;
    protected _languageId: string = this._siteContextService.languageId;
    protected _machineName: string = this._siteContextService.machineName;
    protected _applicationName: string =
        this._siteContextService.applicationName;
    protected _applicationVersion: string =
        this._siteContextService.applicationVersion;
    protected _applicationIdentifier: string =
        this._siteContextService.applicationIdentifier;
    private _userRoleId: number | null = null;

    protected abstract getAppSpecificApiData(): any;

    constructor() {}

    /**
     * Handles site initialization by checking if the current site is a master site.
     * If it's a master site, navigates to site selection page to allow user to choose a specific site.
     * This is typically called during application startup to ensure proper site context.
     */
    async handleSiteInitialization() {
        const isMasterSite =
            this._siteContextService.selectedLocation?.IsMasterSite;
        if (isMasterSite) {
            this.navigateToSiteSelection();
        }
    }

    /**
     * Loads initial application data by performing authentication and fetching required API data.
     * This is the main initialization method that orchestrates the startup sequence:
     * 1. Authenticates the user (client-side or server-side based on platform)
     * 2. Fetches required API data using the obtained authentication token
     * Handles errors gracefully and logs them to console.
     */
    async loadInitialData() {
        try {
            // Step 1: Authenticate user
            if (isPlatformServer(this._platformId))
                await this.authenticateUserServer();
            else await this.authenticateUser();
            // Step 2: Fetch required API data with new token
            await this.fetchApiData();
        } catch (error) {
            console.error('Failed to load initial data', error);
        }
    }

    /**
     * Authenticates user on the client-side platform.
     * Builds authentication payload with machine name, language ID, and site ID.
     * Calls the authentication service and stores the returned WebApiToken and UserRoleId.
     * This method is used when running in browser environment.
     */
    private async authenticateUser() {
        const loginPayload = {
            machineName: this._machineName,
            languageId: this._languageId,
            siteId: this._siteId,
        };

        this._securityTokensServiceDL.buildApiParams(loginPayload);
        await firstValueFrom(this._securityTokensServiceDL.load());

        this._securityTokensServiceDL.subscribeToData(
            (data: AuthenticateSystemUsersDTOModel) => {
                this.storeWebApiToken(data.WebApiToken);
                this.storeUserRoleId(data.UserRoleId);
            }
        );
    }

    /**
     * Stores the WebApiToken in memory and as a cookie for persistence.
     * The token is used for subsequent API calls to authenticate requests.
     * @param token - The authentication token received from the server
     */
    private storeWebApiToken(token: string) {
        this._webApiToken = token;
        this._cookieService.setCookie('webApiToken', this._webApiToken);
    }

    /**
     * Stores the user role ID in memory and as a cookie for persistence.
     * The role ID determines the user's permissions and access levels within the application.
     * @param userRoleId - The numeric role ID assigned to the authenticated user
     */
    private storeUserRoleId(userRoleId: number) {
        this._userRoleId = userRoleId;
        this._cookieService.setCookie(
            'userRoleId',
            this._userRoleId.toString()
        );
    }

    /**
     * Authenticates user on the server-side platform using environment credentials.
     * Builds authentication payload with machine name, language ID, site ID, and server credentials.
     * Uses LOGIN_ID and PASSWORD from environment variables for server-side authentication.
     * Calls the server authentication service and stores the returned WebApiToken and UserRoleId.
     * This method is used when running in server-side rendering (SSR) environment.
     */
    private async authenticateUserServer() {
        const loginPayload: EncryptedSystemUsersConfig = {
            machineName: this._machineName,
            siteId: this._siteId,
            loginId: process.env['LOGIN_ID'] || '',
            password: process.env['PASSWORD'] || '',
            applicationName: this._applicationName,
            applicationVersion: this._applicationVersion,
            applicationIdentifier: this._applicationIdentifier,
            languageId: this._languageId,
        };

        this._authenticateSystemUsersServerServiceDL.buildApiParams(
            loginPayload
        );
        await firstValueFrom(
            this._authenticateSystemUsersServerServiceDL.load()
        );

        this._authenticateSystemUsersServerServiceDL.subscribeToData(
            (data: AuthenticateSystemUsersDTOModel) => {
                this.storeWebApiToken(data.WebApiToken);
                this.storeUserRoleId(data.UserRoleId);
            }
        );
    }

    /**
     * Fetches required API data after successful authentication.
     * Validates that WebApiToken is available before making API calls.
     * Loads parafait default container and site views data in parallel.
     * Updates site context service with current language and site IDs.
     * Throws error if API token is missing.
     */
    private async fetchApiData() {
        if (!this.webApiToken$) {
            throw new Error('Missing API token');
        }
        this._parafaitDefaultContainerServiceDL.buildApiParams({
            siteId: this._siteId,
        });

        Promise.all([
            firstValueFrom(this._parafaitDefaultContainerServiceDL.load()),
            firstValueFrom(this._siteViewsServiceDL.load()),
        ]);

        this._siteContextService.languageId = this._languageId;
        this._siteContextService.siteId = this.siteId$;
    }

    /**
     * Navigates to the site selection page (root route).
     * Only performs navigation when running in browser environment.
     * Used when the current site is identified as a master site and user needs to select a specific site.
     */
    navigateToSiteSelection() {
        if (isPlatformBrowser(this._platformId)) {
            this._router.navigate(['/']);
        }
    }

    /**
     * Updates the site ID and reloads initial data if the site has changed.
     * Compares the new site ID with the current one and cookie value.
     * If the site ID is different or the cookie site ID is null (user logged out),
     * updates the site ID and triggers a complete data reload.
     * @param newSiteId - The new site ID to switch to
     */
    async updateSiteId(newSiteId: number) {
        const cookieSiteId = this._cookieService.getCookie('siteId');
        // alse check if the cookie site id is null, because when the user is logged out, cookie site id is null, so we need to load the initial data
        if (
            !areEqual(newSiteId, this._siteId) ||
            areEqual(cookieSiteId, null)
        ) {
            this.siteId$ = newSiteId;
            await this.loadInitialData();
        }
    }

    /**
     * Updates the language ID and reloads initial data if the language has changed.
     * Compares the new language ID with the current one.
     * If different, updates the language ID and triggers a complete data reload
     * to fetch localized content for the new language.
     * @param newLanguageId - The new language ID to switch to
     */
    async updateLanguageId(newLanguageId: string) {
        if (!areEqual(newLanguageId, this._languageId)) {
            this.languageId$ = newLanguageId;
            await this.loadInitialData();
        }
    }

    /**
     * Setter for site ID with validation and persistence.
     * Updates internal site ID, stores it in cookies, and updates site context service.
     * Only updates if the provided value is truthy.
     * @param value - The site ID to set
     */
    set siteId$(value: number) {
        if (value) {
            this._siteId = value;
            this._cookieService.setCookie('siteId', value.toString());
            this._siteContextService.siteId = value;
        }
    }

    /**
     * Getter for the current site ID.
     * @returns The current site ID
     */
    get siteId$(): number {
        return this._siteId;
    }

    /**
     * Setter for WebApiToken with trimming.
     * Trims whitespace from the token value before storing.
     * @param value - The API token to set
     */
    set webApiToken$(value: string | null) {
        if (value) this._webApiToken = value.trim();
    }

    /**
     * Getter for the current WebApiToken.
     * @returns The current API token or null if not set
     */
    get webApiToken$(): string | null {
        return this._webApiToken;
    }

    /**
     * Getter for the current language ID.
     * @returns The current language ID
     */
    get languageId$(): string {
        return this._languageId;
    }

    /**
     * Setter for language ID with validation and context update.
     * Updates internal language ID and site context service.
     * Only updates if the provided value is truthy.
     * @param value - The language ID to set
     */
    set languageId$(value: string) {
        if (value) {
            this._languageId = value;
            this._siteContextService.languageId = value;
        }
    }
}

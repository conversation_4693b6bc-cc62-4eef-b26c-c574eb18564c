/**
 * @fileoverview Site views business layer service
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-07-16
 */

import { inject, Injectable, signal } from '@angular/core';
import { Router } from '@angular/router';
import { SiteViewsDTOModel } from '../../models/site-views-dto.model';
import { SiteViewsServiceDL } from '../data-layer/site-views-dl.service';
import { AppInitBaseService, CookieService } from 'lib-app-core';

/**
 * Business layer service for managing site views and site selection functionality.
 * Handles site data loading, filtering, and navigation logic for site selection workflows.
 * Provides methods for initializing site views, filtering sites, and managing site selection process.
 */
@Injectable({ providedIn: 'root' })
export class SiteViewsServiceBL {
    private _siteViewsDL = inject(SiteViewsServiceDL);
    private _siteViews = signal<SiteViewsDTOModel[]>([]);
    private _appInitService = inject(AppInitBaseService);
    private _router = inject(Router);
    readonly selectedSiteId = signal<number | null>(null);
    private _cookieService = inject(CookieService);

    /**
     * Getter for the current site views data.
     * @returns Array of site view models
     */
    get siteViews() {
        return this._siteViews();
    }

    /**
     * Initializes site views by subscribing to data layer service.
     * Filters out master sites and stores only regular sites in the signal.
     * This method should be called during application initialization to load available sites.
     */
    initializeSiteViews() {
        this._siteViewsDL.subscribeToData((siteViews: SiteViewsDTOModel[]) => {
            this._siteViews.set(
                siteViews.filter((site) => site.IsMasterSite === false)
            );
        });
    }

    /**
     * Filters site views based on search term matching site name or address.
     * Performs case-insensitive search on both site name and address fields.
     * Returns sites that contain the search term in either field.
     * @param searchTerm - The search term to filter the site views
     * @returns Array of filtered site view models matching the search criteria
     */
    filterSiteViews(searchTerm: string) {
        return this._siteViews().filter((site) => {
            const name = site.SiteName?.toLowerCase() || '';
            const address = site.SiteAddress?.toLowerCase() || '';
            return name.includes(searchTerm) || address.includes(searchTerm);
        });
    }

    /**
     * Selects a site and navigates to the appropriate page based on user authentication status.
     * Updates the site ID through the app initialization service and checks for existing user ID.
     * Navigates to waivers list if user is authenticated, otherwise redirects to registration.
     * @param siteId - The ID of the site to select
     */
    async selectSite(siteId: number) {
        await this._appInitService.updateSiteId(siteId);
        const id = this._cookieService.getCookie('userId');
        if (id) {
            this._router.navigate(['/waivers/list']);
        } else {
            this._router.navigate(['/auth/register'], { replaceUrl: true });
        }
    }

    /**
     * Handles site selection with loading state management and error handling.
     * Sets the selected site ID signal, attempts site selection, and manages loading state.
     * Provides error handling and ensures the selected site ID is reset regardless of success or failure.
     * @param siteId - The ID of the site to select
     */
    async onSiteSelect(siteId: number) {
        this.selectedSiteId.set(siteId);
        try {
            await this.selectSite(siteId);
        } catch (error) {
            console.error('Error selecting site:', error);
        } finally {
            this.selectedSiteId.set(null);
        }
    }
}

/**
 * @fileoverview
 *
 * <AUTHOR>
 * @version 1.0.0
 */

import { Injectable } from '@angular/core';
import { appCoreConstant } from 'lib-app-core';
import { Observable, tap } from 'rxjs';
import { AuthenticateSystemUsersDTOModel } from '../../models/authenticate-system-users-dto.model';
import { ApiServiceBase } from './api-service-base-dl.service';

export type SecurityTokensApiParams = {
    siteId: number;
    machineName: string;
    languageId: string;
    ipAddress?: string;
};

@Injectable({ providedIn: 'root' })
export class SecurityTokensServiceDL extends ApiServiceBase {
    private _apiParams!: SecurityTokensApiParams;
    private _apiData: any;
    constructor() {
        super(
            appCoreConstant.TRANSFER_STATE_KEYS.SECURITY_TOKENS_DATA,
            'SECURITY_TOKENS'
        );
        this.init();
    }

    buildApiParams(data: SecurityTokensApiParams) {
        this._apiParams = data;
    }

    load(): Observable<any> {
        const url = `${this.getApiUrl()}`;
        const loginPayload = this._apiParams;
        return this._http.post<any>(url, loginPayload).pipe(
            tap((response) => {
                // const dtoList = response?.data;
                const raw = response?.data; // { PosMachineGuid: "...", WebApiToken: "...", … }
                this._apiData = AuthenticateSystemUsersDTOModel.fromSingle(raw);
                // console.log(this._apiData)
                this.storeData(this._apiData); // store for client
            })
        );
    }

    /** Safely get the current cached value from the BehaviorSubject */
    // protected get currentData(): any | null {
    //     return this._dataSubject.getValue();
    // }

    // get webApiToken$(): string | null {
    //     return this.currentData?.data.WebApiToken ?? null;
    // }

    // get languageId$(): number | null {
    //     return this.currentData?.data.LanguageId ?? null;
    // }

    // get userId$(): string | null {
    //     return this.currentData?.data.UserId ?? null;
    // }

    // get siteId$(): number | null {
    //     return this.currentData?.data.SiteId ?? null;
    // }

    // get customerGuid$(): string | null {
    //     return this.currentData?.data.CustomerGuid ?? null;
    // }
}

// this.authenticateSystemUsersService.load().subscribe(() => {
//   const token = this.authenticateSystemUsersService.getWebApiToken();
//   const langId = this.authenticateSystemUsersService.getLanguageId();
//   console.log({ token, langId });
// });

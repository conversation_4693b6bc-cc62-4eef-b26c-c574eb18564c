/**
 * @fileoverview
 *
 * <AUTHOR>
 * @version 1.0.0
 */

import { inject, Injectable } from '@angular/core';
import { Observable, switchMap, tap } from 'rxjs';
import { ApiServiceBase } from './api-service-base-dl.service';
import { AuthenticateSystemUsersDTOModel } from '../../models/authenticate-system-users-dto.model';
import { appCoreConstant } from 'lib-app-core';
import {
    EncryptedSystemUsersConfig,
    LoginEncryptionService,
} from 'lib/lib-auth/src/lib/services/login-encryption.service';

@Injectable({ providedIn: 'root' })
export class AuthenticateSystemUsersServerServiceDL extends ApiServiceBase {
    private _apiParams!: EncryptedSystemUsersConfig;
    private _apiData: any;
    private readonly loginEncryptionService = inject(LoginEncryptionService);

    constructor() {
        super(
            appCoreConstant.TRANSFER_STATE_KEYS.AUTHENTICATE_SYSTEM_USERS_DATA,
            'AUTHENTICATE_SYSTEM_USERS'
        );
        this.init();
    }

    buildApiParams(data: EncryptedSystemUsersConfig) {
        this._apiParams = data;
    }

    load(): Observable<any> {
        const url = `${this.getApiUrl()}`;
        const loginPayload = this._apiParams;
        return this.loginEncryptionService.encryptSystemUsers(loginPayload).pipe(
            switchMap((encryptedPayload) =>
                {
                    return this._http.post<any>(url, encryptedPayload).pipe(
                        tap((response) => {
                            // const dtoList = response?.data;
                            const raw = response?.data; // { PosMachineGuid: "...", WebApiToken: "...", … }
                            this._apiData = AuthenticateSystemUsersDTOModel.fromSingle(raw);
                            // console.log(this._apiData)
                            this.storeData(this._apiData); // store for client
                        })
                    );
                }
            )
        );  

    }
}

// this.authenticateSystemUsersService.load().subscribe(() => {
//   const token = this.authenticateSystemUsersService.getWebApiToken();
//   const langId = this.authenticateSystemUsersService.getLanguageId();
//   console.log({ token, langId });
// });

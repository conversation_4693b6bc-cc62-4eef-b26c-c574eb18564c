/**
 * @fileoverview Site context service to get the site id, language id and user id
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-08-12
 */

import { inject, Injectable, signal } from '@angular/core';
import { CookieService, DEFAULT_APP_CONFIG_TOKEN, LanguageContainerServiceDL, SiteViewsDTOModel, SiteViewsServiceDL } from 'lib-app-core';

@Injectable({ providedIn: 'root' })
export class SiteContextService {
    private _defaultAppConfig = inject(DEFAULT_APP_CONFIG_TOKEN);
    private _siteViewsServiceDL = inject(SiteViewsServiceDL);
    private _languageContainerServiceDL = inject(LanguageContainerServiceDL);
    private _cookieService = inject(CookieService);

    private _siteId = signal<number>(this.initializeSiteId());
    private _languageId = signal<string>(this._defaultAppConfig['languageId']);
    private _machineName = signal<string>(this._defaultAppConfig['machineName']);
    private _applicationName = signal<string>(this._defaultAppConfig['applicationName']);
    private _applicationVersion = signal<string>(this._defaultAppConfig['applicationVersion']);
    private _applicationIdentifier = signal<string>(this._defaultAppConfig['applicationIdentifier']);

    private _selectedLocation = signal<SiteViewsDTOModel | null>(null);

    constructor() {
        this._siteViewsServiceDL.subscribeToData(
            (data: SiteViewsDTOModel[]) => {
                this.selectedLocation =
                    data.find((site) => site.SiteId === this._siteId()) || null;
                this._languageContainerServiceDL.buildApiParams({
                    siteId: this._siteId(),
                });
                this._languageContainerServiceDL.load().subscribe();
            }
        );
    }

    private initializeSiteId(): number {
        const cookieSiteId = this._cookieService.getCookie('siteId');
        if (cookieSiteId && !isNaN(parseInt(cookieSiteId))) {
            return parseInt(cookieSiteId);
        }
        return this._defaultAppConfig['siteId'];
    }

    get selectedLocation(): SiteViewsDTOModel | null {
        return this._selectedLocation();
    }

    set selectedLocation(value: SiteViewsDTOModel | null) {
        this._selectedLocation.set(value);
    }

    get siteId(): number {
        return this._siteId();
    }

    set siteId(value: number) {
        this._siteId.set(value);
    }

    get languageId(): string {
        return this._languageId();
    }

    set languageId(value: string) {
        this._languageId.set(value);
    }

    get machineName(): string {
        return this._machineName();
    }

    get applicationName(): string {
        return this._applicationName();
    }

    get applicationVersion(): string {
        return this._applicationVersion();
    }

    get applicationIdentifier(): string {
        return this._applicationIdentifier();
    }

    get webApiToken(): string | null {
        return this._cookieService.getCookie('webApiToken');
    }
}

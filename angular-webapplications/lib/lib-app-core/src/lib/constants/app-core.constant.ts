/**
 * @fileoverview
 *
 * <AUTHOR>
 * @version 1.0.0
 */
export const appCoreConstant = {
    SAMPLE: '',
    API_ENDPOINTS: {
        NODE_AUTHENTICATE_SYSTEM_USERS: '/api/authenticate-system-users',
        SECURITY_TOKENS: '/v2/security/securitytokens',
        AUTHENTICATE_SYSTEM_USERS: '/Login/Secure/AuthenticateSystemUsers',
        LANGUAGE_CONTAINER: '/Configuration/LanguageContainer',
        LANGUAGE_CONTAINER_JSON: '/{siteId}/language-container.json',
        SITE_VIEWS: '/Organization/SiteViews',
        SITE_VIEWS_JSON: '/site-view.json',
        PARAFAIT_DEFAULT_CONTAINER:
            '/Configuration/ParafaitDefaultContainer/Values',
        PARAFAIT_DEFAULT_CONTAINER_JSON:
            '/{siteId}/parafait-default-container.json',
        CUSTOMER_UI_METADA<PERSON>_JSON:
            '/{siteId}/{languageId}/customer-field-config-container.json',
        COUNTRY_CONTAINER_JSON: '/{siteId}/countries-container.json',
        CREATE_CUSTOMER: '/Customer/CustomerList',
        UPLOAD_PROFILE_PICTURE: '/Customer/Image?customerId={customerId}',
        RICH_CONTENTS: '/CommonServices/RichContents',
        ADD_MINOR: '/Customer/Customers/{customerId}/Relationships',
        PREVIEW_WAIVER_JSON:
            '/{siteId}/{languageId}/preview-waiver/preview-waiver.json',
        PREVIEW_HTML_WAIVER_JSON:
            '/{siteId}/{languageId}/preview-waiver/preview-html-waiver/preview-waiver-{waiverSetId}.json',
    },
    TRANSFER_STATE_KEYS: {
        AUTHENTICATE_SYSTEM_USERS_DATA: 'authenticate_system_users_data',
        COUNTRY_CONTAINER_DATA: 'country_container_data',
        CUSTOMER_UI_METADATA: 'customer_ui_metadata',
        PARAFAIT_DEFAULT_CONTAINER_DATA: 'parafait_default_container_data',
        SITE_VIEWS_DATA: 'site_views_data',
        LANGUAGE_CONTAINER_DATA: 'language_container_data',
        CREATE_CUSTOMER_DATA: 'create_customer_data',
        UPLOAD_PROFILE_PICTURE: 'upload_profile_picture',
        RICH_CONTENTS_DATA: 'rich_contents_data',
        ADD_MINOR_DATA: 'add_minor_data',
        PREVIEW_WAIVER_DATA: 'preview_waiver_data',
        PREVIEW_HTML_WAIVER_DATA: 'preview_html_waiver_data',
        SECURITY_TOKENS_DATA: 'security_tokens_data',
    },
    COUNTRY_CONTAINER_DTO_LIST: 'CountryContainerDTOList',
    CUSTOMER_UI_METADATA_DTO_LIST: 'CustomerUIMetadataContainerDTOList',
    PARAFAIT_DEFAULT_CONTAINER_DTO_LIST: 'ParafaitDefaultContainerDTOList',
    SITE_VIEWS_DTO_LIST: 'SiteViewsDTOList',
    LANGUAGE_CONTAINER_DTO_LIST: 'LanguageContainerDTOList',
    RICH_CONTENTS_DTO_LIST: 'RichContentsDTOList',

    IMAGES: {
        PLACEHOLDER: 'assets/images/placeholder.png',
    },

    MAX_IMAGE_FILE_SIZE: 1 * 1024 * 1024, // 1MB
    ACCEPTED_IMAGE_FILE_TYPES: 'image/*',
    IP_ADDRESS_REGEX:
        /^(?:25[0-5]|2[0-4]\d|[0-1]?\d?\d)(?:\.(?:25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}$/,
};

/**
 * ================================================
 * USAGE
 * ================================================
 * use this utility function to get constants
 *
 * Ex:
 * getConstantValue();
 * getConstantValue('SAMPLE');
 * getConstantValue('API_ENDPOINTS.AUTHENTICATE_SYSTEM_USERS');
 * ================================================
 */

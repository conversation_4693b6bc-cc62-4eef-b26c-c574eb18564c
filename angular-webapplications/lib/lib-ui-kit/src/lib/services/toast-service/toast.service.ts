/**
 * @fileoverview Toast Service for displaying toast messages of different types (success, error, warning, info)
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-09-04
 */
import { isPlatformBrowser } from '@angular/common';
import { inject, Injectable, signal, PLATFORM_ID } from '@angular/core';

export interface Toast {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
}

@Injectable({
    providedIn: 'root',
})
export class ToastService {
    private toasts = signal<Toast[]>([]);
    private toastCounter = 0;
    private readonly DEFAULT_DURATION = 5000;
    private readonly PLATFORM_ID = inject(PLATFORM_ID);

    // Map of toastId → timeoutId
    private timeoutMap = new Map<string, any>();

    getToasts = this.toasts.asReadonly();

    private generateId(): string {
        return `toast-${++this.toastCounter}`;
    }

    showToast(toast: Omit<Toast, 'id'>) {
        const id = this.generateId();
        const newToast: Toast = { ...toast, id };

        this.toasts.update((toasts) => [...toasts, newToast]);

        // Auto remove after duration
        const duration = toast.duration ?? this.DEFAULT_DURATION;
        if (duration > 0 && isPlatformBrowser(this.PLATFORM_ID)) {
            const timeoutId = setTimeout(() => {
                this.removeToast(id);
            }, duration);
            this.timeoutMap.set(id, timeoutId);
        }
    }

    removeToast(id: string) {
        this.toasts.update((toasts) =>
            toasts.filter((toast) => toast.id !== id)
        );

        // Clear timeout if exists
        const timeoutId = this.timeoutMap.get(id);
        if (timeoutId) {
            clearTimeout(timeoutId);
            this.timeoutMap.delete(id);
        }
    }

    success(title: string, message: string, duration?: number) {
        this.showToast({ type: 'success', title, message, duration });
    }
    error(title: string, message: string, duration?: number) {
        this.showToast({ type: 'error', title, message, duration });
    }
    warning(title: string, message: string, duration?: number) {
        this.showToast({ type: 'warning', title, message, duration });
    }
    info(title: string, message: string, duration?: number) {
        this.showToast({ type: 'info', title, message, duration });
    }
}

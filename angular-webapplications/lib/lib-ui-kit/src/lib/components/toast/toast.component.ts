/**
 * @fileoverview Toast Component for displaying toast messages
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-09-03
 */
import { Component, input, output, signal, OnInit, ChangeDetectionStrategy } from '@angular/core';
import { Toast } from '../../services/toast-service/toast.service';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-toast',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './toast.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ToastComponent implements OnInit {
  toast = input.required<Toast>();
  close = output<string>();

  isVisible = signal(false);
  isHiding = signal(false);

  private closed = false;

  ngOnInit() {
    // Trigger entrance animation
    setTimeout(() => this.isVisible.set(true), 10);

    // Automatically hide the toast after a delay if duration is set
    const duration = this.toast().duration ?? 3000; // Default to 3000ms if not specified
    if (duration && duration > 0) {
      setTimeout(() => {
        if (this.isVisible()) {
          this.onClose();
        }
      }, duration);
    }
  }

  onClose() {
    if (this.closed) return;
    this.closed = true;

    this.isHiding.set(true);
    setTimeout(() => {
      this.close.emit(this.toast().id);
    }, 300); // match CSS exit animation duration
  }

  getIcon() {
    switch (this.toast().type) {
      case 'success':
        return 'assets/icons/green-tick.svg';
      case 'error':
        return 'assets/icons/close-red.svg';
      case 'warning':
        return 'assets/icons/warning.svg';
      case 'info':
        return 'assets/icons/info.svg';
      default:
        return '';
    }
  }
}

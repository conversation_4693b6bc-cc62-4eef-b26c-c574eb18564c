<div
  [class]="'bg-surface-white border-l-4 rounded-lg shadow-md mb-3 w-full max-w-[340px] md:min-w-[400px] md:max-w-[500px] opacity-0 transform translate-x-full transition-all duration-300 ease-in-out sm:min-w-full sm:max-w-full sm:ml-6 ' + 
    (toast().type === 'success' ? 'border-l-feedback-success' : 
     toast().type === 'error' ? 'border-l-feedback-error' : 
     toast().type === 'warning' ? 'border-l-feedback-warning' : 
     'border-l-feedback-info')"
  [class.opacity-100]="isVisible()"
  [class.transform-none]="isVisible()"
  [class.opacity-0]="isHiding()"
  [class.translate-x-full]="isHiding()">
  <div class="flex items-start p-4 gap-3">
    <div class="flex-shrink-0 mt-0.5">
      <img [src]="getIcon()" alt="" class="h-5 w-5" />
    </div>

    <div class="flex-1 min-w-0">
      <div class="font-semibold text-sm text-primary">{{ toast().title }}</div>
      <div class="text-sm text-neutral-dark leading-relaxed">{{ toast().message }}</div>
    </div>

    <button 
      (click)="onClose()">
      <img src="assets/icons/close-black.svg" alt="Close" class="h-5 w-5" />
    </button>
  </div>
</div>

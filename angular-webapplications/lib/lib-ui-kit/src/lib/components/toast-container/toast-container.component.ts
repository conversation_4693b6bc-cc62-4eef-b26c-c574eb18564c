/**
 * @fileoverview Toast Container Component for displaying multiple toast messages
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-09-04
 */
import { Component, inject, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ToastService } from '../../services/toast-service/toast.service';
import { ToastComponent } from '../toast/toast.component';

@Component({
    selector: 'app-toast-container',
    standalone: true,
    imports: [CommonModule, ToastComponent],
    templateUrl: './toast-container.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ToastContainerComponent {
    protected readonly toastService = inject(ToastService);

    onToastClose(toastId: string): void {
        this.toastService.removeToast(toastId);
    }
}

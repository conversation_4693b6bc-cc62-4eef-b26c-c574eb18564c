import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { DATE_FORMAT, DatePickerComponent, ErrorMessage, PhoneInputComponent, TextInputComponent } from 'lib-ui-kit';
interface ProfileFormData {
    firstName: FormControl<string | null>;
    lastName: FormControl<string | null>;
    dateOfBirth: FormControl<string | null>;
    email: FormControl<string | null>;
    mobileNumber: FormControl<string | null>;
}

@Component({
    selector: 'lib-profile',
    imports: [
        CommonModule,
        ReactiveFormsModule,
        TextInputComponent,
        DatePickerComponent,
        PhoneInputComponent,
    ],
    templateUrl: './profile.component.html',
    styleUrl: './profile.component.css'
})
export class ProfileComponent {
    protected readonly dateFormat = DATE_FORMAT.DD_MM_YYYY;
    profile = {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '25-12-2001',
        email: '<EMAIL>',
        mobileNumber: '8813563780',
        countryCode: '+1',
    };

    isEditMode = false;
    profileForm: FormGroup;

    // Error messages for form validation
    errorMessages: ErrorMessage<ProfileFormData> = {
        email: {
            required: 'Email is required',
            email: 'Please enter a valid email address',
        },
        mobileNumber: {
            required: 'Mobile number is required',
        },
        firstName: {
            required: 'First name is required',
        },
        lastName: {
            required: 'Last name is required',
        },
        dateOfBirth: {
            required: 'Date of birth is required',
        },
    };

    constructor(private fb: FormBuilder) {
        this.profileForm = this.fb.group({
            firstName: [this.profile.firstName, Validators.required],
            lastName: [this.profile.lastName, Validators.required],
            dateOfBirth: [this.profile.dateOfBirth, Validators.required],
            email: [this.profile.email, [Validators.required, Validators.email]],
            mobileNumber: [this.profile.mobileNumber, Validators.required],
        });
    }

    toggleEditMode(): void {
        this.isEditMode = !this.isEditMode;

        if (this.isEditMode) {
            // Reset form with current profile values when entering edit mode
            this.profileForm.patchValue({
                firstName: this.profile.firstName,
                lastName: this.profile.lastName,
                dateOfBirth: this.profile.dateOfBirth,
                email: this.profile.email,
                mobileNumber: this.profile.mobileNumber,
            });
        }
    }

    updateProfile(): void {
        if (this.profileForm.valid) {
            // Update profile with form values
            const formValues = this.profileForm.value;
            this.profile = {
                ...this.profile,
                ...formValues,
            };

            // Exit edit mode
            this.isEditMode = false;
        } else {
            // Mark all fields as touched to show validation errors
            this.profileForm.markAllAsTouched();
        }
    }

    cancelEdit(): void {
        // Exit edit mode without saving changes
        this.isEditMode = false;
    }
}

/**
 * @fileoverview Base service for handling encryption and decryption
 * <AUTHOR>
 * @version 1.0.0
 */

import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, forkJoin, map } from 'rxjs';
import { DEFAULT_APP_CONFIG_TOKEN, EnvService, SiteContextService } from 'lib-app-core';
import CryptoES, { WordArray } from 'crypto-es';
import { JSEncrypt } from 'jsencrypt';

@Injectable()
export abstract class EncryptionBaseService {
    private _siteContextService = inject(SiteContextService);
    protected http = inject(HttpClient);
    protected envService = inject(EnvService);
    protected defaultAppConfig = inject(DEFAULT_APP_CONFIG_TOKEN);

    protected aesKey: WordArray | null = null;
    protected jsEncrypt: JSEncrypt | null = null;
    protected secondsSinceEpoch: number | null = null;
    protected _encryptionParams: any;

    /**
     * Abstract method that must be implemented by derived classes
     * to handle the encryption process with specific configuration
     * @param config - Configuration object containing encryption parameters
     * @returns Observable of encrypted data
     */
    abstract encrypt(config: any): Observable<any>;

    /**
     * Stores encryption parameters for use during the encryption process
     * @param params - Parameters to be stored for encryption
     */
    buildEncryptionParams(params: any) {
        this._encryptionParams = params;
    }

    /**
     * Getter for accessing stored encryption parameters
     * @returns The stored encryption parameters
     */
    get encryptionParams() {
        return this._encryptionParams;
    }

    /**
     * Initializes RSA and AES key setup by fetching server time and public key
     * Sets up JSEncrypt instance and retrieves necessary encryption parameters
     * @returns Observable that completes when initialization is done
     */
    protected initializeEncryption(): Observable<void> {
        this.jsEncrypt = new JSEncrypt();

        return forkJoin({
            time: this.getSecondsSinceEpoch(),
            key: this.getPublicKey(),
        }).pipe(map(() => void 0));
    }

    /**
     * Fetches the current server time in seconds since epoch
     * Used for timestamp validation in encryption process
     * @returns Observable that updates secondsSinceEpoch property
     */
    private getSecondsSinceEpoch(): Observable<void> {
        const url = `${this.envService.parafaitApiBaseUrl}/Login/SecondsSinceEpoch`;
        return this.http.get<{ data: number }>(url).pipe(
            map((res) => {
                this.secondsSinceEpoch = res.data;
            })
        );
    }

    /**
     * Fetches the RSA public key from the server for asymmetric encryption
     * Configures the JSEncrypt instance with the retrieved public key
     * @returns Observable that completes when public key is set
     */
    private getPublicKey(): Observable<void> {
        const url = `${this.envService.parafaitApiBaseUrl}/Login/PublicKey`;
        const params = {
            siteId: this._siteContextService.siteId.toString(),
            application: this._siteContextService.applicationName,
            version: this._siteContextService.applicationVersion,
            identifier: this._siteContextService.applicationIdentifier,
            format: 'PEM',
        };
        return this.http.get<{ data: string }>(url, { params }).pipe(
            map((res) => {
                if (!this.jsEncrypt) {
                    this.jsEncrypt = new JSEncrypt();
                }
                this.jsEncrypt.setPublicKey(res.data);
            })
        );
    }

    /**
     * Generates a random AES key for symmetric encryption
     * @param bits - Key size in bits (default: 256)
     */
    protected generateAESKey(bits: number = 256) {
        this.aesKey = CryptoES.lib.WordArray.random(bits / 8);
    }

    /**
     * Encrypts a message using AES encryption with CBC mode and PKCS7 padding
     * @param message - The plain text message to encrypt
     * @returns Base64 encoded encrypted string
     * @throws Error if AES key is not initialized
     */
    protected aesEncrypt(message: string): string {
        if (!this.aesKey) throw new Error('AES key not initialized');
        const iv = CryptoES.enc.Hex.parse('00000000000000000000000000000000');
        const encrypted = CryptoES.AES.encrypt(message, this.aesKey, {
            iv,
            mode: CryptoES.mode.CBC,
            padding: CryptoES.pad.Pkcs7,
        });

        if (!encrypted.ciphertext) {
            throw new Error('AES encryption failed: ciphertext is undefined');
        }

        return encrypted.ciphertext.toString(CryptoES.enc.Base64);
    }

    /**
     * Encrypts a message using RSA encryption with the configured public key
     * @param message - The plain text message to encrypt
     * @returns Encrypted string or empty string if encryption fails
     * @throws Error if JSEncrypt is not initialized
     */
    protected rsaEncrypt(message: string): string {
        if (!this.jsEncrypt) throw new Error('JSEncrypt not initialized');
        return this.jsEncrypt.encrypt(message) || '';
    }
}

/**
 * @fileoverview Business layer service for handling password reset functionality
 * <AUTHOR>
 * @version 1.0.0
 */

import { inject, Injectable } from '@angular/core';
import { LibAuthPasswordResetDL } from '../data-layer/lib-auth-password-reset-dl.service';

import { RequestState } from 'lib/lib-app-core/src/lib/utils/request-state';

import { LibUserDetailDL } from '../data-layer/lib-auth-user-details-dl.service';
import { map } from 'rxjs';
import { LibAuthSecurityTokenDL } from '../data-layer/lib-auth-security-token.service';
import {
    ContactDTO,
    ResetPasswordPayload,
} from '../interfaces/forgot-password.interface';
import { UserLoginDTOModel } from '../models/user-login-dto.model';

@Injectable()
export class LibAuthPasswordResetBL extends RequestState {
    private _libAuthPasswordResetDL = inject(LibAuthPasswordResetDL);

    private _libGetUserDetailDL = inject(LibUserDetailDL);
    private _libAuthSecurityTokenDL = inject(LibAuthSecurityTokenDL);

    /**
     * Validates a password reset token
     *
     * This method sends a request to validate a password reset token.
     * The request is handled through the data layer service and the response is returned.
     *
     * @param token - The token to validate
     * @returns Observable<any> - The response from the data layer service
     */
    validatePasswordToken(token: string) {
        this._libAuthSecurityTokenDL.buildApiParams({
            Token: token,
        });
        return this._libAuthSecurityTokenDL.load();
    }

    /**
     * Resets a user's password
     *
     * This method sends a request to reset a user's password.
     * The request is handled through the data layer service and the response is returned.
     *
     * @param newPassword - The new password to set for the user
     * @param profileData - The profile data of the user
     * @returns Observable<any> - The response from the data layer service
     */
    resetPassword(newPassword: string, profileData: UserLoginDTOModel) {
        const transformedProfileData = this.transformProfileDataForReset(
            profileData,
            newPassword
        );

        this._libAuthPasswordResetDL.buildApiParams(transformedProfileData);
        return this._libAuthPasswordResetDL.load();
    }

    /**
     * Transforms the profile data to the format required by resetPassword API
     *
     * @param profileData - Raw profile data from getUserDetails
     * @param newPassword - The new password to set
     * @returns Transformed profile data in the required format
     */
    private transformProfileDataForReset(
        profileData: UserLoginDTOModel,
        newPassword: string
    ): ResetPasswordPayload {
        const contactDTOList = this.createContactDTOList(profileData);
        const profileDTO = this.createProfileDTO(
            profileData,
            newPassword,
            contactDTOList
        );

        return {
            PhoneNumber: profileData.PhoneNumber,
            Password: newPassword,
            SiteId: profileData.SiteId,
            Nationality: 'Indian',
            ContactDTOList: contactDTOList,
            ProfileDTO: profileDTO,
            Verified: true,
            Email: profileData.Email,
        };
    }

    /**
     * Creates the contact DTO list for both customer and profile
     *
     * @param profileData - The profile data containing contact information
     * @returns Array of contact DTOs
     */
    private createContactDTOList(profileData: UserLoginDTOModel) {
        return [
            {
                ContactTypeId: 2,
                ContactType: 1,
                Attribute1: profileData.Email,
            },
            {
                ContactTypeId: 1,
                ContactType: 2,
                Attribute1: profileData.PhoneNumber,
            },
        ];
    }

    /**
     * Creates the profile DTO with all required fields
     *
     * @param profileData - The profile data
     * @param newPassword - The new password
     * @param contactDTOList - The contact DTO list
     * @returns Profile DTO object
     */
    private createProfileDTO(
        profileData: UserLoginDTOModel,
        newPassword: string,
        contactDTOList: ContactDTO[]
    ) {
        return {
            FirstName: profileData.ProfileDTO?.FirstName,
            Password: newPassword,
            LastName: profileData.ProfileDTO?.LastName,
            DateOfBirth: profileData.ProfileDTO?.DateOfBirth,
            IsActive: true,
            IsChanged: true,
            IsChangedRecursive: true,
            Id: -1,
            ContactDTOList: contactDTOList,
        };
    }

    /**
     * Gets user details by GUID
     *
     * This method sends a request to get user details by GUID.
     * The request is handled through the data layer service and the response is returned.
     *
     * @param guid - The GUID of the user to get details for
     * @returns Observable<any> - The response from the data layer service
     */
    getUserDetails(guid: string) {
        this._libGetUserDetailDL.buildApiParams({
            guid,
        });
        return this._libGetUserDetailDL
            .load()
            .pipe(map((data) => data?.data[0] || null));
    }
}

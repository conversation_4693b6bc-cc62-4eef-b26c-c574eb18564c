<div class="flex flex-col gap-5">
    <p i18n="login-form.add-details" class="text-sm text-neutral-dark">
        Add the below details to get an account. We don't share or sell your
        data.
    </p>

    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
        <div class="flex flex-col space-y-4 max-w-[23rem]">
            <!-- Email Address -->
            <lib-text-input formControlName="email" id="email" label="Email Address" type="email"
                placeholder="Enter email address" [errorMessages]="errorMessages.email"></lib-text-input>

            <!-- Password -->
            <lib-text-input formControlName="password" id="password" label="Password" type="password"
                placeholder="Enter password" [showPasswordToggle]="true"
                [errorMessages]="errorMessages.password"></lib-text-input>

            <!-- Forgot Password  -->
            <button class="self-start text-blue-500 underline" (click)="showForgotPassword.set(true)" type="button">
                <span i18n="login-form.forgot-password">Forgot password?</span>
            </button>
            @if(loginErrorMessage()) {
            <p i18n="login-form.error-message" class="max-w-[23rem] text-center mt-2 text-sm text-red-500">
                {{loginErrorMessage()}}
            </p>
            }
        </div>

        <!-- Desktop CTA -->
        <div class="hidden md:block">
            <hr class="my-4 text-surface" />
            <ng-container *ngTemplateOutlet="loginCTA"></ng-container>
        </div>
    </form>
</div>

<!-- Footer CTA (Mobile) -->
<lib-page-footer>
    <ng-container *ngTemplateOutlet="loginCTA"></ng-container>
</lib-page-footer>


<ng-template #loginCTA>
    <div class="flex flex-col md:flex-row items-center gap-4">
        <!-- Login Button -->
        <button type="button" [disabled]="loginForm.invalid  || loading()" (click)="onSubmit()"
            class="w-full md:max-w-[300px] py-3 px-4 text-white font-medium bg-primary rounded-4xl disabled:bg-surface">
            <span i18n="login-form.login-button">{{ loading() ? "Logging in..." : "Login" }}</span>
        </button>
        <p class="flex items-center gap-1 text-sm text-center text-primary">
            <span i18n="login-form.dont-have-account">Don't have an account?</span>
            <a i18n="login-form.sign-up" routerLink="/auth/register" class="text-secondary-blue underline">Sign up</a>
        </p>
    </div>
</ng-template>

<!-- Forgot Password Modal -->
<ng-template #forgotPasswordContent>
    <lib-forgot-password />
</ng-template>

<lib-modal [isOpen]="showForgotPassword()" [modalContent]="forgotPasswordContent" (closeModal)="closeForgotPassword()">
</lib-modal>
<lib-modal [isOpen]="showActionSelectionModal()" [modalContent]="actionSelectionContent"
    (closeModal)="closeActionSelectionModal()" dialogueHeader="Choose what you'd like to do!">
</lib-modal>

<!-- Action Selection Modal -->
<ng-template #actionSelectionContent>
    <app-login-action-selection (signWaiverSelected)="onSignWaiverSelected()" (checkInSelected)="onCheckInSelected()">
    </app-login-action-selection>
</ng-template>
/**
 * @fileoverview Component for handling user login functionality with form validation and navigation
 * <AUTHOR>
 * @version 1.0.0
 */

import { CommonModule } from '@angular/common';
import {
    Component,
    TemplateRef,
    computed,
    effect,
    inject,
    signal,
} from '@angular/core';
import {
    FormBuilder,
    FormGroup,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import {
    ErrorMessage,
    ModalComponent,
    PageFooterComponent,
    TextInputComponent,
} from 'lib-ui-kit';
import { formValidationMessages } from 'lib/lib-app-core/src/lib/constants/form-validation-message';
import { LibAuthLoginBL } from 'lib/lib-auth/src/lib/business-layer/lib-auth-login-bl.service';
import { LibAuthLoginDL } from '../../data-layer/lib-auth-login-dl.service';
import { LibUserDetailDL } from '../../data-layer/lib-auth-user-details-dl.service';
import { LoginFormData } from '../../interfaces/login-form.interface';
import { ForgotPasswordComponent } from '../forgot-password/forgot-password.component';
import { STRONG_PASSWORD_REGEX } from '../registration-form-base/registration-form-base.component';
import { LoginEncryptionService } from '../../services/login-encryption.service';
import { WaiverRoutingServiceBL } from 'projects/online-waiver/src/app/services/business-layer/waiver-routing-bl.service';
import { LoginActionSelectionComponent } from 'projects/online-waiver/src/app/components/login-action-selection/login-action-selection-modal.component';

export type ErrorMessagLogin<T> = {
    [K in keyof T]?: Record<string, string | TemplateRef<Component> | null>;
};

@Component({
    selector: 'lib-login-form',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ForgotPasswordComponent,
        TextInputComponent,
        ModalComponent,
        PageFooterComponent,
        RouterLink,
        LoginActionSelectionComponent,
    ],
    templateUrl: './login-form.component.html',
    styleUrl: './login-form.component.css',
    providers: [
        LibAuthLoginBL,
        LibAuthLoginDL,
        LibUserDetailDL,
        LoginEncryptionService,
        WaiverRoutingServiceBL,
    ],
})
export class LoginFormComponent {
    readonly showForgotPassword = signal(false);
    readonly showActionSelectionModal = signal(false);
    readonly loginForm: FormGroup;
    readonly _authBL = inject(LibAuthLoginBL);
    private readonly _waiverRoutingService = inject(WaiverRoutingServiceBL);
    private readonly _router = inject(Router);
    readonly loading = computed(
        () => this._authBL.loading() || this._authBL.routingServiceLoading()
    );
    readonly loginErrorMessage = this._authBL.errorMessage;

    errorMessages: ErrorMessage<LoginFormData> = {
        email: formValidationMessages.email,
        password: formValidationMessages.password,
    };

    constructor(private fb: FormBuilder) {
        this.loginForm = this.fb.group({
            email: ['', [Validators.required, Validators.email]],
            password: [
                '',
                [
                    Validators.required,
                    Validators.pattern(STRONG_PASSWORD_REGEX),
                ],
            ],
        });

        /**
         * Subscribes to the form value changes and resets the login error state
         * on form change
         */
        this.loginForm.valueChanges.subscribe(() => {
            // Reset login error state on form change
            if (this.loginErrorMessage()) {
                this._authBL.errorMessage.set(null);
            }
        });

        // Watch for successful login to show action selection modal
        effect(() => {
            if (this._authBL.loginSuccess() && !this.loading()) {
                // Show action selection modal after successful login
                this.showActionSelectionModal.set(true);
            }
        });
    }

    /**
     * Handles the form submission for the login process
     *
     * This method validates the form and triggers the login process
     * if the form is valid.
     */
    onSubmit() {
        const { email, password } = this.loginForm.value;
        this._authBL.login(email, password);
        // Reset form values after login attempt
        this.loginForm.reset();
    }

    closeForgotPassword() {
        this.showForgotPassword.set(false);
    }

    closeActionSelectionModal() {
        this.showActionSelectionModal.set(false);
    }

    onSignWaiverSelected() {
        this.showActionSelectionModal.set(false);
        // Set userId cookie after user selection
        this._authBL.setUserIdCookie();
        // Call the waiver routing service to handle post-login routing
        this._waiverRoutingService.handlePostLoginRouting();
    }

    onCheckInSelected() {
        this.showActionSelectionModal.set(false);
        // Set userId cookie after user selection
        this._authBL.setUserIdCookie();
        // Navigate to signed waivers page for now (since check-in route doesn't exist yet)
        this._router.navigate(['/waivers/check-in']);
    }
}

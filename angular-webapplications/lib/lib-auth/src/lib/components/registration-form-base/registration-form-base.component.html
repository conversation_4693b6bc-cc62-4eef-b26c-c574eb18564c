<div>
    <p i18n="registration-form-base.add-details" class="text-sm text-neutral-dark mb-6">
        Add the below details to get an account. We don't share or sell your
        data.
    </p>

    <form [formGroup]="registerForm()" (ngSubmit)="submit.emit(registerForm())">
        @if(isFieldsLoading()) {
            <!-- Loading skeleton -->
            <lib-skeleton-loader   
            [count]="12"
            wrapperClass="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            skeletonClass="animate-pulse rounded-xl h-10 bg-surface-lightest"
            />

        }@else {    
            <lib-dynamic-form
            [fields]="fields()"
            [form]="registerForm()"
            [errorMessages]="errorMessages()"
        />
        }
        <fieldset class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-5 md:mb-6">
            <!-- Password -->
            <lib-text-input
                formControlName="password"
                [id]="'registration-password'"
                label="Password"
                type="password"
                placeholder="Enter password"
                [showPasswordToggle]="true"
                [description]="fieldDescription()?.password"
                [errorMessages]="errorMessages()?.password"
            ></lib-text-input>

            <!-- Confirm Password -->
            <lib-text-input 
                formControlName="confirmPassword"
                [id]="'registration-confirm-password'"
                label="Confirm Password"
                type="password"
                placeholder="Re-enter password"
                [showPasswordToggle]="true"
                [errorMessages]="errorMessages()?.confirmPassword"
            ></lib-text-input>
        </fieldset>

        <!-- Minors -->
        <ng-content select="[add-minor-form]"></ng-content>

        @if(optInPromotionsField()) {
        <div class="flex flex-col gap-2 mb-4 rounded-3xl bg-surface-lightest p-2 md:max-w-[305px]">

            <!-- Promotion Card -->
            <div class="relative overflow-hidden rounded-xl w-full">
                <img src="assets/images/opt-promotion.jpg" alt="Promotion" class="w-full h-36 object-cover scale-125" />
                <div class="absolute inset-0 flex items-end p-5 text-surface-white bg-gradient-to-top-black">
                    <h3 class="text-xl font-medium max-w-[80%]">
                        Get 50% off on your next party booking
                    </h3>
                </div>
            </div>

            <!-- Opt-in Checkbox -->

            <lib-checkbox
                [id]="optInPromotionsField()?.id ?? ''"
                formControlName="{{ optInPromotionsField()?.fieldName }}"
                [label]="optInPromotionsField()?.label ?? ''"
                [required]="optInPromotionsField()?.required ?? false"
                customClass="w-[18px] h-[18px]"
                ></lib-checkbox>
        </div>
        }
        
        @if(termsAndConditionsField()) {
        <div class="px-2">
            <app-terms-and-conditions
                [hasTermsAndConditions]="!!termsAndConditionsField()"
                (termsAccepted)="termsAccepted.emit($event)"
            ></app-terms-and-conditions>
        </div>
        }

        <!-- Register Button -->
        <hr class="my-6 text-surface hidden md:block" />

        <!-- Desktop CTA -->
        <div class="hidden md:block">
            <ng-container *ngTemplateOutlet="registrationCTA"></ng-container>
        </div>

        <ng-template #registrationCTA>
            <div class="flex flex-col md:flex-row items-center gap-4">
                <!-- Login Button -->
                <button
                    [disabled]="registerForm().invalid || isSubmitting()"
                    (click)="submit.emit(registerForm())"
                    type="button"
                    class="w-full md:max-w-[300px] py-3 px-4 bg-primary text-white font-medium rounded-4xl disabled:bg-surface"
                >
                  <span i18n="registration-form-base.register-button">{{ isSubmitting() ? 'Registering...' : 'Register and sign waiver' }}</span>
                </button>
                <p class="text-sm text-center md:text-right flex items-center gap-1">
                    <span i18n="registration-form-base.already-have-account">Already have an account?</span>
                    <a i18n="registration-form-base.login" routerLink="/auth/login" class="text-blue-600 underline">Login</a>
                </p>
            </div>
        </ng-template>
    </form>
</div>

<!-- Footer CTA (Mobile) -->
<lib-page-footer>
    <ng-container *ngTemplateOutlet="registrationCTA"></ng-container>
</lib-page-footer>

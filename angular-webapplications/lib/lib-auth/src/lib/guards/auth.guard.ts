/**
 * @fileoverview Guard for checking if the user is logged in and is a authenticated user
 * <AUTHOR>
 * @version 1.0.0
 */
import { inject, Injectable } from '@angular/core';
import { CanActivate, Router, UrlTree } from '@angular/router';
import { CookieService } from 'lib/lib-app-core/src/lib/services/cookie-service/cookie.service';

@Injectable({
    providedIn: 'root',
})
export class AuthGuard implements CanActivate {
    private _cookieService = inject(CookieService);
    private _router = inject(Router);

    canActivate(): boolean | UrlTree {
       const token = this._cookieService.getCookie('webApiToken');
        const id = this._cookieService.getCookie('customerId');
        
        //Checks if the user is logged in by verifying the presence of the webApiToken and customerId cookies
        //If the cookies are present, the user is considered logged in and the method returns true
        //If the cookies are not present, the user is redirected to the login page and the method returns false
       
        if (token && id) {
            return true;
        } else {
            this._router.navigate(['/auth/login']);
            return false;
        }
    }
}

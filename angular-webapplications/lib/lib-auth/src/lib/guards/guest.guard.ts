/**
 * @fileoverview Guard for checking if the user is a guest and not a authenticated user
 * <AUTHOR>
 * @version 1.0.0
 */
import { inject, Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { CookieService } from 'lib/lib-app-core/src/lib/services/cookie-service/cookie.service';

@Injectable({ providedIn: 'root' })
export class GuestGuard implements CanActivate {
    private _cookieService = inject(CookieService);
    private _router = inject(Router);

  canActivate(): boolean {
    const token = this._cookieService.getCookie('webApiToken');
    const id = this._cookieService.getCookie('customerId');
    //Checks if the user is logged in by verifying the presence of the webApiToken and customerId cookies
    //If the cookies are present, the user is redirected to the waivers page and the method returns false
    //If the cookies are not present, the user is considered a guest and the method returns true
    if (token && id) {
      this._router.navigate(['/waivers/list']);
      return false;
    }
    return true;
  }
}

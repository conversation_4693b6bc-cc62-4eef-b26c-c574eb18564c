/**
 * @fileoverview Data layer service for handling login API calls
 * <AUTHOR>
 * @version 1.0.0
 */
import { inject, Injectable } from '@angular/core';
import { ApiServiceBase, SiteContextService } from 'lib-app-core';
import { Observable, switchMap } from 'rxjs';
import { LibAuthLoginAPIParams } from '../interfaces/login-form.interface';
import {
    LoginEncryptionConfig,
    LoginEncryptionService,
} from '../services/login-encryption.service';

@Injectable()
export class LibAuthLoginDL extends ApiServiceBase {
    private _apiParams!: LibAuthLoginAPIParams;
    private readonly loginEncryptionService = inject(LoginEncryptionService);
    private readonly siteContextService = inject(SiteContextService);
    /**
     * Initializes the login data layer service
     * Sets up the API endpoint and action type for user login operations
     */
    constructor() {
        // Call the parent constructor with the specific API endpoint and action type
        super('user_login_data', 'USER_LOGIN');
        this.init();
    }

    /**
     * Builds and stores the API parameters for the login request
     * This method is called by the business layer to set the parameters before making the API call
     * @param data - Login parameters containing username and password
     */
    buildApiParams(data: LibAuthLoginAPIParams) {
        this._apiParams = data;
    }

    /**
     * Executes the login API call with encrypted credentials
     *
     * Process:
     * 1. Constructs the API URL for login endpoint
     * 2. Creates encryption configuration with user credentials and app metadata
     * 3. Encrypts the login data using hybrid encryption (AES + RSA)
     * 4. Sends encrypted payload to the server
     *
     * @returns Observable of the server response containing authentication result
     */
    load(): Observable<any> {
        const url = `${this.getApiUrl()}`;

        // Create encryption config using the new LoginEncryptionConfig interface
        const encryptionConfig: LoginEncryptionConfig = {
            siteId: this.siteContextService.siteId,
            applicationName: this.siteContextService.applicationName,
            applicationVersion: this.siteContextService.applicationVersion,
            applicationIdentifier: this.siteContextService.applicationIdentifier,
            loginId: this._apiParams.UserName.trim(),
            password: this._apiParams.Password.trim(),
            machineName: this.siteContextService.machineName,
        };

        return this.loginEncryptionService
            .encrypt(encryptionConfig)
            .pipe(
                switchMap((encryptedPayload) =>
                    this._http.post(url, encryptedPayload)
                )
            );
    }
}

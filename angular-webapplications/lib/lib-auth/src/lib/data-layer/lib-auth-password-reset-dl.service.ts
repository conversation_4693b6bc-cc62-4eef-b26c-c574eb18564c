/**
 * @fileoverview Data layer service for handling password reset API calls
 * <AUTHOR>
 * @version 1.0.0
 */

import { Injectable } from '@angular/core';
import { ApiServiceBase } from 'lib-app-core';
import { Observable } from 'rxjs';
import { ResetPasswordPayload } from '../interfaces/forgot-password.interface';

@Injectable()
export class LibAuthPasswordResetDL extends ApiServiceBase {
    private _apiParams!: ResetPasswordPayload;

    constructor() {
        super('password_reset_data', 'PASSWORD_RESET');
        this.init();
    }

    buildApiParams(data: ResetPasswordPayload) {
        this._apiParams = data;
    }

    //Returns the API URL for the password reset request
    //This method is used to construct the URL for the API call
    load(): Observable<any> {
        const url = `${this.getApiUrl()}`;
        const payload = this._apiParams;

        return this._http.post<any>(url, payload);
    }
}
